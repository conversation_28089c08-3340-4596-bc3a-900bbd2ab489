#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env' })
import { readFileSync } from 'fs'
import { join } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// Get current directory
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('')
  console.error('Please add these to your .env file:')
  console.error('NEXT_PUBLIC_SUPABASE_URL=your_supabase_url')
  console.error('SUPABASE_SERVICE_ROLE_KEY=your_service_role_key')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function executeSQL(sql: string, description: string) {
  console.log(`🔄 ${description}...`)
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: sql
    })

    if (error) {
      console.error(`❌ Failed to ${description.toLowerCase()}:`, error.message)
      throw error
    }

    console.log(`✅ ${description} completed`)
  } catch (error) {
    console.error(`💥 Error during ${description.toLowerCase()}:`, error)
    throw error
  }
}

async function createExecSqlFunction() {
  console.log('⚠️  Note: You may need to create the exec_sql function manually.')
  console.log('If the setup fails, please run this SQL in your Supabase SQL Editor:')
  console.log('')
  console.log('CREATE OR REPLACE FUNCTION exec_sql(sql text)')
  console.log('RETURNS void')
  console.log('LANGUAGE plpgsql')
  console.log('SECURITY DEFINER')
  console.log('AS $$')
  console.log('BEGIN')
  console.log('  EXECUTE sql;')
  console.log('END;')
  console.log('$$;')
  console.log('')
}

async function setupDatabase() {
  try {
    console.log('🚀 Setting up Clothing Store database...')
    console.log('📍 Supabase URL:', SUPABASE_URL)
    console.log('')

    // Test connection
    console.log('🔍 Testing database connection...')
    const { error: testError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1)

    if (testError) {
      console.error('❌ Failed to connect to database:', testError.message)
      process.exit(1)
    }
    console.log('✅ Database connection successful')

    // Create exec_sql function
    await createExecSqlFunction()

    // Load and execute initial schema
    const schemaPath = join(__dirname, '..', 'migrations', '001_initial_schema.sql')
    const schemaSql = readFileSync(schemaPath, 'utf-8')
    await executeSQL(schemaSql, 'Creating database schema')

    // Load and execute seed data
    const seedPath = join(__dirname, '..', 'migrations', '002_seed_data.sql')
    const seedSql = readFileSync(seedPath, 'utf-8')
    await executeSQL(seedSql, 'Inserting seed data')

    console.log('')
    console.log('🎉 Database setup completed successfully!')
    console.log('')
    console.log('📋 What was created:')
    console.log('   ✅ Categories table with sample categories')
    console.log('   ✅ Colors table with predefined colors')
    console.log('   ✅ Products table with S3 image support')
    console.log('   ✅ Product images table for multiple images')
    console.log('   ✅ Admin users table for authentication')
    console.log('   ✅ Row Level Security (RLS) policies')
    console.log('   ✅ Database indexes for performance')
    console.log('   ✅ Sample products for testing')
    console.log('')
    console.log('🔑 Next steps:')
    console.log('   1. Create an admin user by running: npm run create-admin')
    console.log('   2. Configure your AWS S3 credentials in .env')
    console.log('   3. Start the development server: npm run dev')
    console.log('')

  } catch (error) {
    console.error('💥 Database setup failed:', error)
    console.log('')
    console.log('🔧 Troubleshooting:')
    console.log('   1. Check your Supabase credentials in .env')
    console.log('   2. Ensure your Supabase project is active')
    console.log('   3. Verify you have the correct service role key')
    console.log('   4. Try running the SQL migrations manually in Supabase SQL Editor')
    process.exit(1)
  }
}

// Run setup
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase()
}
