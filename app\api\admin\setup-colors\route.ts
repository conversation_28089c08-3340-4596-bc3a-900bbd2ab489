import { createClient } from "@/lib/supabase/server"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    const supabase = await createClient()
    if (!supabase) {
      return NextResponse.json({ error: "No se pudo conectar a la base de datos" }, { status: 500 })
    }

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: "No autorizado" }, { status: 401 })
    }

    // SQL script to create colors table
    const createColorsTableSQL = `
      -- Create colors table for dynamic color management
      CREATE TABLE IF NOT EXISTS public.colors (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT UNIQUE NOT NULL,
          hex_code TEXT NOT NULL,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
      );

      -- Enable RLS for colors table
      ALTER TABLE public.colors ENABLE ROW LEVEL SECURITY;

      -- Create policies for colors
      DROP POLICY IF EXISTS "Anyone can view colors" ON public.colors;
      CREATE POLICY "Anyone can view colors" ON public.colors
          FOR SELECT USING (true);

      DROP POLICY IF EXISTS "Only authenticated users can manage colors" ON public.colors;
      CREATE POLICY "Only authenticated users can manage colors" ON public.colors
          FOR ALL USING (auth.role() = 'authenticated');

      -- Create index for better performance
      CREATE INDEX IF NOT EXISTS idx_colors_active ON public.colors(is_active);
    `

    // Execute the table creation
    const { error: createError } = await supabase.rpc("exec_sql", { sql: createColorsTableSQL })

    if (createError) {
      console.error("Error creating colors table:", createError)
      // Try alternative approach using individual queries
      const { error: tableError } = await supabase.from("colors").select("id").limit(1)

      if (tableError && tableError.message?.includes("does not exist")) {
        return NextResponse.json(
          {
            error:
              "No se pudo crear la tabla automáticamente. Por favor, ejecuta el script SQL manualmente en Supabase.",
          },
          { status: 500 },
        )
      }
    }

    // Insert default colors
    const defaultColors = [
      { name: "Negro", hex_code: "#000000" },
      { name: "Blanco", hex_code: "#FFFFFF" },
      { name: "Azul", hex_code: "#3B82F6" },
      { name: "Rosa", hex_code: "#EC4899" },
      { name: "Beige", hex_code: "#F5F5DC" },
      { name: "Crema", hex_code: "#F5F5DC" },
      { name: "Azul Marino", hex_code: "#1E3A8A" },
      { name: "Rosa Palo", hex_code: "#F8BBD9" },
      { name: "Gris", hex_code: "#6B7280" },
      { name: "Verde", hex_code: "#10B981" },
      { name: "Amarillo", hex_code: "#F59E0B" },
      { name: "Morado", hex_code: "#8B5CF6" },
      { name: "Rojo", hex_code: "#EF4444" },
      { name: "Naranja", hex_code: "#F97316" },
      { name: "Café", hex_code: "#92400E" },
    ]

    // Insert colors one by one to handle conflicts
    let insertedCount = 0
    for (const color of defaultColors) {
      const { error: insertError } = await supabase.from("colors").insert(color).select()

      if (!insertError) {
        insertedCount++
      }
    }

    return NextResponse.json({
      message: `Sistema de colores configurado exitosamente. ${insertedCount} colores agregados.`,
      success: true,
    })
  } catch (error) {
    console.error("Error setting up colors:", error)
    return NextResponse.json(
      {
        error: "Error interno del servidor",
      },
      { status: 500 },
    )
  }
}
