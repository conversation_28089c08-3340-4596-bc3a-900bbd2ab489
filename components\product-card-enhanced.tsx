"use client"

import { useState } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog"
import { MessageCircle, Eye, ChevronLeft, ChevronRight } from "lucide-react"
import { generateWhatsAppMessage } from "@/lib/whatsapp"

interface ProductImage {
  id: string
  image_url: string
  alt_text: string | null
  is_primary: boolean
  sort_order: number
}

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  sizes: string[]
  colors: string[]
  color_hex: string | null
  stock_quantity: number
  is_available: boolean
  product_images?: ProductImage[]
}

interface ProductCardEnhancedProps {
  product: Product
}

export function ProductCardEnhanced({ product }: ProductCardEnhancedProps) {
  const [galleryOpen, setGalleryOpen] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const isOutOfStock = product.stock_quantity === 0 || !product.is_available
  const isLowStock = product.stock_quantity > 0 && product.stock_quantity <= 3

  const allImages =
    product.product_images && product.product_images.length > 0
      ? product.product_images.sort((a, b) => a.sort_order - b.sort_order).map((img) => img.image_url)
      : product.image_url
        ? [product.image_url]
        : []

  const primaryImage = allImages[0] || getPlaceholderImage()

  function getPlaceholderImage() {
    const query = `elegant ${product.category.toLowerCase()} ${product.colors?.[0]?.toLowerCase() || ""} minimalist fashion clothing`
    return `/placeholder.svg?height=400&width=300&query=${encodeURIComponent(query)}`
  }

  const handleWhatsAppClick = () => {
    const message = generateWhatsAppMessage(product)
    const phoneNumber = "573001234567"
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const handleImageClick = () => {
    if (allImages.length > 1) {
      setGalleryOpen(true)
    }
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
  }

  return (
    <>
      <Card className="group overflow-hidden border-border hover:shadow-xl transition-all duration-300 bg-card">
        <div className="relative aspect-[3/4] overflow-hidden bg-muted">
          <Image
            src={primaryImage || "/placeholder.svg"}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500 cursor-pointer"
            onClick={handleImageClick}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = getPlaceholderImage()
            }}
          />

          {allImages.length > 1 && (
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
              <Button size="sm" variant="secondary" className="rounded-full h-10 w-10 p-0" onClick={handleImageClick}>
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          )}

          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {isOutOfStock && (
              <Badge variant="destructive" className="text-xs font-medium">
                Agotado
              </Badge>
            )}
            {isLowStock && !isOutOfStock && (
              <Badge
                variant="secondary"
                className="text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
              >
                ¡Últimas {product.stock_quantity}!
              </Badge>
            )}
            {allImages.length > 1 && (
              <Badge variant="outline" className="text-xs bg-background/90 backdrop-blur-sm">
                +{allImages.length - 1} fotos
              </Badge>
            )}
          </div>

          <div className="absolute top-2 right-2">
            <Badge variant="outline" className="text-xs bg-background/90 backdrop-blur-sm">
              {product.category}
            </Badge>
          </div>
        </div>

        <CardContent className="p-4 space-y-3 min-h-[140px] flex flex-col">
          <div className="flex items-start justify-between">
            <h3 className="font-medium text-foreground text-sm leading-tight line-clamp-2 flex-1 mr-2">
              {product.name}
            </h3>
            <div className="text-right">
              <p className="font-bold text-lg text-foreground">DOP${product.price.toLocaleString()}</p>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            {product.sizes && product.sizes.length > 0 && (
              <div className="flex gap-1">
                {product.sizes.slice(0, 3).map((size) => (
                  <Badge key={size} variant="outline" className="text-xs px-2 py-1">
                    {size}
                  </Badge>
                ))}
                {product.sizes.length > 3 && (
                  <Badge variant="outline" className="text-xs px-2 py-1">
                    +{product.sizes.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {product.colors && product.colors.length > 0 && (
              <div className="flex gap-1">
                {product.colors.slice(0, 2).map((color, index) => (
                  <Badge key={color} variant="outline" className="text-xs px-2 py-1 flex items-center gap-1">
                    <div
                      className="w-2 h-2 rounded-full border"
                      style={{
                        backgroundColor: getColorHex(color, index === 0 ? product.color_hex : null),
                      }}
                    />
                    {color}
                  </Badge>
                ))}
                {product.colors.length > 2 && (
                  <Badge variant="outline" className="text-xs px-2 py-1">
                    +{product.colors.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>

          {product.description && (
            <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed flex-1">{product.description}</p>
          )}

          <div className="flex items-center justify-between text-xs mt-auto">
            <span
              className={`font-medium ${
                isOutOfStock ? "text-destructive" : isLowStock ? "text-orange-600" : "text-green-600"
              }`}
            >
              {isOutOfStock ? "Sin stock" : `${product.stock_quantity} disponibles`}
            </span>
            <span className="text-muted-foreground flex items-center gap-1">🚚 Envío nacional</span>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 h-[60px] flex items-center">
          <Button
            onClick={handleWhatsAppClick}
            disabled={isOutOfStock}
            className="w-full bg-green-600 hover:bg-green-700 disabled:bg-muted text-white flex items-center gap-2 font-medium"
            size="sm"
          >
            <MessageCircle className="h-4 w-4" />
            {isOutOfStock ? "Producto Agotado" : "Comprar por WhatsApp"}
          </Button>
        </CardFooter>
      </Card>

      <Dialog open={galleryOpen} onOpenChange={setGalleryOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{product.name}</DialogTitle>
          </DialogHeader>
          <div className="relative">
            <div className="aspect-square relative bg-muted rounded-lg overflow-hidden">
              <Image
                src={allImages[currentImageIndex] || getPlaceholderImage()}
                alt={`${product.name} - Imagen ${currentImageIndex + 1}`}
                fill
                className="object-cover"
              />
            </div>

            {allImages.length > 1 && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 rounded-full h-10 w-10 p-0 bg-transparent"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full h-10 w-10 p-0 bg-transparent"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>

                <div className="flex justify-center gap-2 mt-4">
                  {allImages.map((_, index) => (
                    <button
                      key={index}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentImageIndex ? "bg-primary" : "bg-muted-foreground/30"
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

function getColorHex(colorName: string, providedHex: string | null): string {
  if (providedHex) return providedHex

  const colorMap: Record<string, string> = {
    negro: "#000000",
    blanco: "#FFFFFF",
    azul: "#3B82F6",
    rosa: "#EC4899",
    verde: "#10B981",
    rojo: "#EF4444",
    amarillo: "#F59E0B",
    morado: "#8B5CF6",
    gris: "#6B7280",
    beige: "#F5F5DC",
    "azul marino": "#1E3A8A",
    "rosa palo": "#F8BBD9",
  }

  return colorMap[colorName.toLowerCase()] || "#6B7280"
}
