import os
import subprocess
import sys

def run_sql_script():
    """Execute the colors table creation script"""
    try:
        # Get the directory of this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sql_file = os.path.join(script_dir, 'create_colors_table.sql')
        
        print("🎨 Configurando sistema de colores dinámico...")
        print("📋 Creando tabla de colores en la base de datos...")
        
        # Read the SQL file
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("✅ Script SQL leído correctamente")
        print("📝 Contenido del script:")
        print("-" * 50)
        print(sql_content)
        print("-" * 50)
        
        print("\n🔧 Para ejecutar este script:")
        print("1. Ve a tu panel de Supabase")
        print("2. Navega a SQL Editor")
        print("3. Copia y pega el contenido del script")
        print("4. Ejecuta el script")
        
        print("\n🎯 El script creará:")
        print("• Tabla 'colors' para gestión dinámica de colores")
        print("• Políticas de seguridad RLS")
        print("• 15 colores predefinidos")
        print("• Índices para mejor rendimiento")
        
        print("\n✨ Una vez ejecutado, podrás:")
        print("• Agregar colores desde el panel admin")
        print("• Editar colores existentes")
        print("• Seleccionar colores dinámicamente en productos")
        print("• Crear colores personalizados al agregar productos")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = run_sql_script()
    if success:
        print("\n🎉 ¡Sistema de colores configurado exitosamente!")
        print("🚀 Ahora puedes gestionar colores dinámicamente desde el panel admin")
    else:
        print("\n💥 Error al configurar el sistema de colores")
        sys.exit(1)
