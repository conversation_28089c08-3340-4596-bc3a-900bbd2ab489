import os
import psycopg2
from urllib.parse import urlparse

def execute_sql_script(cursor, script_content, script_name):
    """Execute a SQL script"""
    try:
        print(f"Ejecutando {script_name}...")
        cursor.execute(script_content)
        print(f"✅ {script_name} ejecutado exitosamente")
        return True
    except Exception as e:
        print(f"❌ Error ejecutando {script_name}: {str(e)}")
        return False

def main():
    # Get database URL from environment
    database_url = os.getenv('POSTGRES_URL')
    if not database_url:
        print("❌ No se encontró POSTGRES_URL en las variables de entorno")
        return
    
    try:
        # Parse the database URL
        parsed = urlparse(database_url)
        
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            database=parsed.path[1:],  # Remove leading slash
            user=parsed.username,
            password=parsed.password
        )
        
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("🔗 Conectado a la base de datos")
        
        # Complete database setup script
        complete_setup_script = """
        -- Create products table
        CREATE TABLE IF NOT EXISTS public.products (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            image_url TEXT,
            category TEXT NOT NULL,
            size TEXT,
            color TEXT,
            stock_quantity INTEGER NOT NULL DEFAULT 0,
            is_available BOOLEAN NOT NULL DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Create admin_users table
        CREATE TABLE IF NOT EXISTS public.admin_users (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Enable Row Level Security
        ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Anyone can view products" ON public.products;
        DROP POLICY IF EXISTS "Only authenticated users can manage products" ON public.products;
        DROP POLICY IF EXISTS "Only authenticated users can view admin users" ON public.admin_users;

        -- Create policies for products table
        CREATE POLICY "Anyone can view products" ON public.products
            FOR SELECT USING (true);

        CREATE POLICY "Only authenticated users can manage products" ON public.products
            FOR ALL USING (auth.role() = 'authenticated');

        -- Create policies for admin_users table  
        CREATE POLICY "Only authenticated users can view admin users" ON public.admin_users
            FOR SELECT USING (auth.role() = 'authenticated');

        -- Function to update updated_at timestamp
        CREATE OR REPLACE FUNCTION public.handle_updated_at()
        RETURNS trigger
        LANGUAGE plpgsql
        SECURITY DEFINER
        SET search_path = public
        AS $$
        BEGIN
          NEW.updated_at = timezone('utc'::text, now());
          RETURN NEW;
        END;
        $$;

        -- Trigger for products updated_at
        DROP TRIGGER IF EXISTS handle_products_updated_at ON public.products;
        CREATE TRIGGER handle_products_updated_at
          BEFORE UPDATE ON public.products
          FOR EACH ROW
          EXECUTE FUNCTION public.handle_updated_at();
        """
        
        execute_sql_script(cursor, complete_setup_script, "Configuración completa de tablas")
        
        # Insert sample products (only if table is empty)
        insert_products_script = """
        INSERT INTO public.products (name, description, price, image_url, category, size, color, stock_quantity, is_available)
        SELECT * FROM (VALUES
            ('Vestido Midi Elegante', 'Vestido midi de corte clásico perfecto para ocasiones especiales', 89.99, '/elegant-midi-dress.jpg', 'Vestidos', 'M', 'Negro', 15, true),
            ('Blusa de Seda Premium', 'Blusa de seda natural con acabados de lujo', 65.50, '/premium-silk-blouse.jpg', 'Blusas', 'S', 'Blanco', 20, true),
            ('Pantalón Wide Leg', 'Pantalón de pierna ancha de tela fluida', 75.00, '/wide-leg-pants.png', 'Pantalones', 'L', 'Beige', 12, true),
            ('Chaqueta Blazer Estructurada', 'Blazer de corte estructurado para look profesional', 120.00, '/structured-blazer-jacket.jpg', 'Chaquetas', 'M', 'Azul Marino', 8, true),
            ('Falda Plisada Midi', 'Falda midi con pliegues y cintura alta', 55.99, '/pleated-midi-skirt.png', 'Faldas', 'S', 'Rosa Palo', 18, true),
            ('Top Crop Minimalista', 'Top crop de diseño minimalista y corte moderno', 35.00, '/minimalist-crop-top.jpg', 'Tops', 'XS', 'Blanco', 25, true),
            ('Camisa Clásica Blanca', 'Camisa de algodón con corte clásico', 45.00, '/white-classic-shirt.jpg', 'Camisas', 'M', 'Blanco', 30, true),
            ('Jeans Skinny Azul', 'Jeans ajustados de mezclilla premium', 90.00, '/blue-skinny-jeans.jpg', 'Jeans', 'L', 'Azul', 22, true),
            ('Vestido Casual Floral', 'Vestido casual con estampado floral', 65.00, '/floral-casual-dress.jpg', 'Vestidos', 'M', 'Multicolor', 18, true),
            ('Chaqueta Denim Clásica', 'Chaqueta de mezclilla con corte clásico', 85.00, '/classic-denim-jacket.png', 'Chaquetas', 'L', 'Azul', 14, true),
            ('Falda Mini Negra', 'Falda mini de tela elástica', 40.00, '/black-mini-skirt.jpg', 'Faldas', 'S', 'Negro', 25, true),
            ('Blusa Manga Larga', 'Blusa elegante de manga larga', 55.00, '/long-sleeve-elegant-blouse.jpg', 'Blusas', 'M', 'Crema', 20, true)
        ) AS v(name, description, price, image_url, category, size, color, stock_quantity, is_available)
        WHERE NOT EXISTS (SELECT 1 FROM public.products LIMIT 1);
        """
        
        execute_sql_script(cursor, insert_products_script, "Insertar productos de ejemplo")
        
        # Insert admin user (only if table is empty)
        insert_admin_script = """
        INSERT INTO public.admin_users (email, password_hash)
        SELECT '<EMAIL>', '$2a$10$rOvHPGkwQGKqvzjo.6.1/.H.S.*******.*******.*******.*******.8'
        WHERE NOT EXISTS (SELECT 1 FROM public.admin_users WHERE email = '<EMAIL>');
        """
        
        execute_sql_script(cursor, insert_admin_script, "Insertar usuario administrador")
        
        # Verify setup
        cursor.execute("SELECT COUNT(*) FROM public.products;")
        product_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM public.admin_users;")
        admin_count = cursor.fetchone()[0]
        
        print(f"✅ Base de datos configurada exitosamente")
        print(f"📦 {product_count} productos en el catálogo")
        print(f"👤 {admin_count} usuario(s) administrador(es)")
        print(f"🔐 Admin login: <EMAIL> / admin123")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {str(e)}")
        print("🔧 Verifica que las variables de entorno de Supabase estén configuradas correctamente")

if __name__ == "__main__":
    main()
