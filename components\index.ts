// Layout Components
export { Header } from './layout/header'
export { Footer } from './layout/footer'

// Product Components
export { ProductCard } from './product/product-card'
export { ProductGrid } from './product/product-grid'
export { ProductImageModal } from './product/product-image-modal'

// Shared Components
export { ThemeProvider } from './shared/theme-provider'
export { WhatsAppFloat } from './shared/whatsapp-float'
export { StoreStats } from './shared/store-stats'
export { DatabaseSetupBanner } from './shared/database-setup-banner'

// Form Components
export { EnhancedAddProductDialog } from './forms/enhanced-add-product-dialog'
export { EditProductDialog } from './forms/edit-product-dialog'

// Admin Components
export { AdminDashboard } from './admin/admin-dashboard'
export { CategoryManager } from './admin/category-manager'
export { ColorManager } from './admin/color-manager'
export { ProductTable } from './admin/product-table'
export { SetupColorsBanner } from './admin/setup-colors-banner'
