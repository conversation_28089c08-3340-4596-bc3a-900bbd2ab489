import { createClient } from "@/lib/supabase/server"
import { ProductGrid } from "@/components/product-grid"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { WhatsAppFloat } from "@/components/whatsapp-float"
import { DatabaseSetupBanner } from "@/components/database-setup-banner"

export default async function HomePage() {
  let supabase
  let products = []
  let categories = []
  let hasError = false
  let errorMessage = ""

  try {
    supabase = await createClient()
    console.log("[v0] Attempting to fetch products from database...")

    const [productsResult, categoriesResult] = await Promise.all([
      supabase.from("products").select("*").eq("is_available", true).order("created_at", { ascending: false }),
      supabase.from("products").select("category").eq("is_available", true),
    ])

    if (productsResult.error || categoriesResult.error) {
      console.error("[v0] Database query error:", productsResult.error || categoriesResult.error)
      hasError = true
      errorMessage = productsResult.error?.message || categoriesResult.error?.message || "Error desconocido"
    } else {
      console.log("[v0] Successfully fetched", productsResult.data?.length || 0, "products")
      products = productsResult.data || []
      // Obtener categorías únicas
      const uniqueCategories = [...new Set(categoriesResult.data?.map((item) => item.category) || [])]
      categories = uniqueCategories
      console.log("[v0] Found categories:", categories)
    }
  } catch (error) {
    console.error("[v0] Database connection error:", error)
    hasError = true
    errorMessage = error instanceof Error ? error.message : "Error de conexión a la base de datos"
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {hasError && <DatabaseSetupBanner />}

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-light text-foreground mb-4 text-balance">TIENDA DE ROPA </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto text-pretty">
            Descubre nuestra cuidada selección de ropa femenina con diseño atemporal y calidad excepcional
          </p>
          {!hasError && (
            <div className="mt-6 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
              <p className="text-sm text-green-700 dark:text-green-300">
                🚚 <strong>Realizamos envíos a todo el país</strong> • Tiempo de entrega: menos de 24 Horas • Compra
                fácil por WhatsApp
              </p>
            </div>
          )}
        </div>

        {hasError ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Configurando la base de datos... Sigue las instrucciones arriba para ver los productos.
            </p>
          </div>
        ) : (
          <ProductGrid products={products} categories={categories} />
        )}
      </main>
      <Footer />
      <WhatsAppFloat />
    </div>
  )
}
