-- Enhanced Database Schema for Minimalist Clothing Store
-- Run this script in your Supabase SQL Editor

-- Create categories table for dynamic category management
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create product_images table for multiple images per product
CREATE TABLE IF NOT EXISTS public.product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL,
    image_url TEXT NOT NULL,
    alt_text TEXT,
    is_primary BOOLEAN NOT NULL DEFAULT false,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Update products table structure
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS sizes TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS colors TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS color_hex TEXT,
ADD COLUMN IF NOT EXISTS category_id UUID;

-- Create foreign key relationship
ALTER TABLE public.products 
ADD CONSTRAINT fk_products_category 
FOREIGN KEY (category_id) REFERENCES public.categories(id);

-- Create foreign key for product images
ALTER TABLE public.product_images 
ADD CONSTRAINT fk_product_images_product 
FOREIGN KEY (product_id) REFERENCES public.products(id) ON DELETE CASCADE;

-- Enable RLS for new tables
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_images ENABLE ROW LEVEL SECURITY;

-- Create policies for categories
CREATE POLICY "Anyone can view categories" ON public.categories
    FOR SELECT USING (true);

CREATE POLICY "Only authenticated users can manage categories" ON public.categories
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for product images
CREATE POLICY "Anyone can view product images" ON public.product_images
    FOR SELECT USING (true);

CREATE POLICY "Only authenticated users can manage product images" ON public.product_images
    FOR ALL USING (auth.role() = 'authenticated');

-- Insert default categories
INSERT INTO public.categories (name, description) VALUES
('Blusas', 'Blusas y camisas elegantes'),
('Vestidos', 'Vestidos para toda ocasión'),
('Pantalones', 'Pantalones y jeans'),
('Faldas', 'Faldas de diferentes estilos'),
('Chaquetas', 'Chaquetas y blazers'),
('Tops', 'Tops y camisetas'),
('Camisas', 'Camisas clásicas'),
('Jeans', 'Pantalones de mezclilla')
ON CONFLICT (name) DO NOTHING;

-- Update existing products to use new structure
UPDATE public.products 
SET sizes = ARRAY[size], 
    colors = ARRAY[color],
    color_hex = CASE 
        WHEN LOWER(color) = 'negro' THEN '#000000'
        WHEN LOWER(color) = 'blanco' THEN '#FFFFFF'
        WHEN LOWER(color) = 'azul' THEN '#3B82F6'
        WHEN LOWER(color) = 'rosa' THEN '#EC4899'
        WHEN LOWER(color) = 'beige' THEN '#F5F5DC'
        WHEN LOWER(color) = 'crema' THEN '#F5F5DC'
        WHEN LOWER(color) = 'azul marino' THEN '#1E3A8A'
        WHEN LOWER(color) = 'rosa palo' THEN '#F8BBD9'
        ELSE '#6B7280'
    END,
    category_id = (SELECT id FROM public.categories WHERE name = products.category LIMIT 1)
WHERE sizes IS NULL OR array_length(sizes, 1) IS NULL;

-- Insert product images for existing products
INSERT INTO public.product_images (product_id, image_url, is_primary, sort_order)
SELECT id, image_url, true, 0
FROM public.products 
WHERE image_url IS NOT NULL
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_product_images_product_id ON public.product_images(product_id);
CREATE INDEX IF NOT EXISTS idx_product_images_primary ON public.product_images(product_id, is_primary);
