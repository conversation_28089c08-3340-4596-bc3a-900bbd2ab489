import { createClient } from "@supabase/supabase-js"
import { NextResponse, type NextRequest } from "next/server"

export async function updateSession(request: NextRequest) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY

  console.log("[v0] Middleware - Environment check:", {
    url: !!supabaseUrl,
    key: !!supabaseAnonKey,
    path: request.nextUrl.pathname,
  })

  const supabaseResponse = NextResponse.next({
    request,
  })

  if (!supabaseUrl || !supabaseAnonKey) {
    console.log("[v0] Middleware - Missing Supabase credentials, skipping auth check")
    return supabaseResponse
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)

    if (request.nextUrl.pathname.startsWith("/admin") && !request.nextUrl.pathname.includes("/login")) {
      // Por ahora permitimos acceso al admin sin verificación compleja
      // La verificación se hará en la página del admin
      return supabaseResponse
    }

    return supabaseResponse
  } catch (error) {
    console.error("[v0] Middleware - Error creating Supabase client:", error)
    return supabaseResponse
  }
}
