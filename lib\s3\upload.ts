import { PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { s3Client, S3_CONFIG } from './config'
import { v4 as uuidv4 } from 'uuid'

export interface UploadResult {
  success: boolean
  url?: string
  key?: string
  error?: string
}

export interface PresignedUrlResult {
  success: boolean
  uploadUrl?: string
  key?: string
  publicUrl?: string
  error?: string
}

/**
 * Generate a presigned URL for direct client-side upload to S3
 */
export async function generatePresignedUploadUrl(
  fileName: string,
  fileType: string,
  fileSize: number
): Promise<PresignedUrlResult> {
  try {
    // Validate file type
    if (!S3_CONFIG.allowedFileTypes.includes(fileType)) {
      return {
        success: false,
        error: `File type ${fileType} is not allowed. Allowed types: ${S3_CONFIG.allowedFileTypes.join(', ')}`
      }
    }

    // Validate file size
    if (fileSize > S3_CONFIG.maxFileSize) {
      return {
        success: false,
        error: `File size ${fileSize} exceeds maximum allowed size of ${S3_CONFIG.maxFileSize} bytes`
      }
    }

    // Generate unique key
    const fileExtension = fileName.split('.').pop()
    const uniqueFileName = `${uuidv4()}.${fileExtension}`
    const key = `${S3_CONFIG.imageFolder}/${uniqueFileName}`

    // Create presigned URL
    const command = new PutObjectCommand({
      Bucket: S3_CONFIG.bucketName,
      Key: key,
      ContentType: fileType,
      ContentLength: fileSize,
    })

    const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }) // 1 hour

    // Generate public URL
    const publicUrl = `https://${S3_CONFIG.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${key}`

    return {
      success: true,
      uploadUrl,
      key,
      publicUrl
    }
  } catch (error) {
    console.error('Error generating presigned URL:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Upload file directly to S3 (server-side)
 */
export async function uploadFileToS3(
  file: Buffer,
  fileName: string,
  fileType: string
): Promise<UploadResult> {
  try {
    // Validate file type
    if (!S3_CONFIG.allowedFileTypes.includes(fileType)) {
      return {
        success: false,
        error: `File type ${fileType} is not allowed`
      }
    }

    // Generate unique key
    const fileExtension = fileName.split('.').pop()
    const uniqueFileName = `${uuidv4()}.${fileExtension}`
    const key = `${S3_CONFIG.imageFolder}/${uniqueFileName}`

    const command = new PutObjectCommand({
      Bucket: S3_CONFIG.bucketName,
      Key: key,
      Body: file,
      ContentType: fileType,
    })

    await s3Client.send(command)

    const url = `https://${S3_CONFIG.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com/${key}`

    return {
      success: true,
      url,
      key
    }
  } catch (error) {
    console.error('Error uploading file to S3:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Delete file from S3
 */
export async function deleteFileFromS3(key: string): Promise<{ success: boolean; error?: string }> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: S3_CONFIG.bucketName,
      Key: key,
    })

    await s3Client.send(command)

    return { success: true }
  } catch (error) {
    console.error('Error deleting file from S3:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Extract S3 key from URL
 */
export function extractS3KeyFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    
    // Remove leading slash
    return pathname.startsWith('/') ? pathname.slice(1) : pathname
  } catch {
    return null
  }
}

/**
 * Validate if URL is from our S3 bucket
 */
export function isS3Url(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname === `${S3_CONFIG.bucketName}.s3.${S3_CONFIG.region}.amazonaws.com`
  } catch {
    return false
  }
}
