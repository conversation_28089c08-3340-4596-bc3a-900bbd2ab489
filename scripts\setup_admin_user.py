import os
from supabase import create_client, Client

def setup_admin_user():
    """
    Crea un usuario admin usando la API de Supabase Admin
    """
    
    # Configuración de Supabase
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_service_key:
        print("❌ Error: Variables de entorno SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas")
        return False
    
    try:
        # Crear cliente con service role key para operaciones admin
        supabase: Client = create_client(supabase_url, supabase_service_key)
        
        print("🔧 Creando usuario admin...")
        
        # Crear usuario admin usando Supabase Auth Admin API
        admin_user = supabase.auth.admin.create_user({
            "email": "<EMAIL>",
            "password": "admin123",
            "email_confirm": True
        })
        
        if admin_user:
            print("✅ Usuario admin creado exitosamente")
            print("📧 Email: <EMAIL>")
            print("🔑 Contraseña: admin123")
            
            # También insertar en nuestra tabla personalizada
            result = supabase.table('admin_users').upsert({
                'email': '<EMAIL>',
                'password_hash': 'supabase_auth_managed'  # Indicamos que la contraseña se maneja por Supabase Auth
            }).execute()
            
            print("✅ Registro en tabla admin_users actualizado")
            return True
        else:
            print("❌ Error al crear usuario admin")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = setup_admin_user()
    if success:
        print("\n🎉 ¡Configuración completada!")
        print("Ahora puedes iniciar sesión con:")
        print("Email: <EMAIL>")
        print("Contraseña: admin123")
    else:
        print("\n💡 Si el error persiste, puedes crear el usuario manualmente en el panel de Supabase:")
        print("1. Ve a Authentication > Users en tu dashboard de Supabase")
        print("2. Haz clic en 'Add user'")
        print("3. Usa email: <EMAIL> y contraseña: admin123")
