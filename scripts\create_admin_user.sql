-- Script para crear usuario admin en Supabase Auth
-- <PERSON>ste script debe ejecutarse en el SQL Editor de Supabase

-- Primero, insertamos el usuario en auth.users (tabla interna de Supabase)
-- Nota: Este es un enfoque simplificado. En producción, deberías usar la API de Supabase Admin
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('admin123', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider":"email","providers":["email"]}',
  '{}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
);

-- <PERSON><PERSON><PERSON> mantenemos el registro en nuestra tabla personalizada para referencia
INSERT INTO public.admin_users (email, password_hash) VALUES
('<EMAIL>', crypt('admin123', gen_salt('bf')))
ON CONFLICT (email) DO UPDATE SET password_hash = EXCLUDED.password_hash;
