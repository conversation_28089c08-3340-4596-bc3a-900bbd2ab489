#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readlineSync } from 'readline-sync'

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

async function createAdminUser() {
  try {
    console.log('👤 Creating Admin User for Clothing Store')
    console.log('=====================================')
    console.log('')

    // Get email from user
    let email: string
    while (true) {
      email = prompt('Enter admin email address: ') || ''
      
      if (!email) {
        console.log('❌ Email is required')
        continue
      }
      
      if (!isValidEmail(email)) {
        console.log('❌ Please enter a valid email address')
        continue
      }
      
      break
    }

    console.log('')
    console.log('🔍 Checking if admin user already exists...')

    // Check if admin user already exists
    const { data: existingAdmin, error: checkError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking existing admin:', checkError.message)
      throw checkError
    }

    if (existingAdmin) {
      console.log('⚠️  Admin user already exists with this email')
      
      if (existingAdmin.is_active) {
        console.log('✅ Admin user is already active')
        return
      } else {
        console.log('🔄 Reactivating admin user...')
        
        const { error: updateError } = await supabase
          .from('admin_users')
          .update({ is_active: true })
          .eq('email', email)

        if (updateError) {
          console.error('❌ Failed to reactivate admin user:', updateError.message)
          throw updateError
        }

        console.log('✅ Admin user reactivated successfully')
        return
      }
    }

    console.log('➕ Creating new admin user...')

    // Create admin user
    const { data: newAdmin, error: createError } = await supabase
      .from('admin_users')
      .insert({
        email: email,
        is_active: true
      })
      .select()
      .single()

    if (createError) {
      console.error('❌ Failed to create admin user:', createError.message)
      throw createError
    }

    console.log('✅ Admin user created successfully!')
    console.log('')
    console.log('📋 Admin User Details:')
    console.log(`   Email: ${newAdmin.email}`)
    console.log(`   ID: ${newAdmin.id}`)
    console.log(`   Created: ${new Date(newAdmin.created_at).toLocaleString()}`)
    console.log('')
    console.log('🔑 Next steps:')
    console.log('   1. The admin user can now sign up/login at /admin/login')
    console.log('   2. They must use the exact email address specified above')
    console.log('   3. After signing up, they will have full admin access')
    console.log('')

  } catch (error) {
    console.error('💥 Failed to create admin user:', error)
    console.log('')
    console.log('🔧 Troubleshooting:')
    console.log('   1. Ensure the database is set up (run: npm run setup-db)')
    console.log('   2. Check your Supabase credentials')
    console.log('   3. Verify the admin_users table exists')
    process.exit(1)
  }
}

function prompt(question: string): string {
  // Simple prompt implementation for Node.js
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  })

  return new Promise((resolve) => {
    readline.question(question, (answer: string) => {
      readline.close()
      resolve(answer.trim())
    })
  }) as any
}

// Alternative implementation using process.stdin
async function getInput(question: string): Promise<string> {
  process.stdout.write(question)
  
  return new Promise((resolve) => {
    process.stdin.once('data', (data) => {
      resolve(data.toString().trim())
    })
  })
}

// Updated createAdminUser function with proper async input
async function createAdminUserAsync() {
  try {
    console.log('👤 Creating Admin User for Clothing Store')
    console.log('=====================================')
    console.log('')

    // Get email from user
    let email: string
    while (true) {
      email = await getInput('Enter admin email address: ')
      
      if (!email) {
        console.log('❌ Email is required')
        continue
      }
      
      if (!isValidEmail(email)) {
        console.log('❌ Please enter a valid email address')
        continue
      }
      
      break
    }

    console.log('')
    console.log('🔍 Checking if admin user already exists...')

    // Check if admin user already exists
    const { data: existingAdmin, error: checkError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking existing admin:', checkError.message)
      throw checkError
    }

    if (existingAdmin) {
      console.log('⚠️  Admin user already exists with this email')
      
      if (existingAdmin.is_active) {
        console.log('✅ Admin user is already active')
        return
      } else {
        console.log('🔄 Reactivating admin user...')
        
        const { error: updateError } = await supabase
          .from('admin_users')
          .update({ is_active: true })
          .eq('email', email)

        if (updateError) {
          console.error('❌ Failed to reactivate admin user:', updateError.message)
          throw updateError
        }

        console.log('✅ Admin user reactivated successfully')
        return
      }
    }

    console.log('➕ Creating new admin user...')

    // Create admin user
    const { data: newAdmin, error: createError } = await supabase
      .from('admin_users')
      .insert({
        email: email,
        is_active: true
      })
      .select()
      .single()

    if (createError) {
      console.error('❌ Failed to create admin user:', createError.message)
      throw createError
    }

    console.log('✅ Admin user created successfully!')
    console.log('')
    console.log('📋 Admin User Details:')
    console.log(`   Email: ${newAdmin.email}`)
    console.log(`   ID: ${newAdmin.id}`)
    console.log(`   Created: ${new Date(newAdmin.created_at).toLocaleString()}`)
    console.log('')
    console.log('🔑 Next steps:')
    console.log('   1. The admin user can now sign up/login at /admin/login')
    console.log('   2. They must use the exact email address specified above')
    console.log('   3. After signing up, they will have full admin access')
    console.log('')

  } catch (error) {
    console.error('💥 Failed to create admin user:', error)
    process.exit(1)
  } finally {
    process.exit(0)
  }
}

// Run create admin
if (import.meta.url === `file://${process.argv[1]}`) {
  createAdminUserAsync()
}
