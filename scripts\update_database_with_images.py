import os
from supabase import create_client, Client

def main():
    # Initialize Supabase client
    url = os.environ.get("SUPABASE_URL")
    key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    
    if not url or not key:
        print("Error: Missing Supabase environment variables")
        return
    
    supabase: Client = create_client(url, key)
    
    try:
        # Execute the SQL to add additional_images column
        with open('scripts/add_additional_images_column.sql', 'r') as file:
            sql_content = file.read()
        
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement:
                result = supabase.rpc('exec_sql', {'sql': statement}).execute()
                print(f"✅ Executed: {statement[:50]}...")
        
        print("✅ Database schema updated successfully!")
        print("✅ Added additional_images column to products table")
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")

if __name__ == "__main__":
    main()
