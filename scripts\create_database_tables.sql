-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category TEXT NOT NULL,
    size TEXT NOT NULL,
    color TEXT NOT NULL,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    is_available BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Anyone can view products" ON public.products
    FOR SELECT USING (true);

-- Create admin_users table
CREATE TABLE IF NOT EXISTS public.admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS for admin_users
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

-- Create policy for admin users
CREATE POLICY "Admins can manage admin_users" ON public.admin_users
    FOR ALL USING (auth.email() IN (SELECT email FROM public.admin_users));

-- Insert sample products
INSERT INTO public.products (name, description, price, category, size, color, stock_quantity, is_available) VALUES
('Blusa Elegante Blanca', 'Blusa de seda con diseño minimalista perfecto para ocasiones especiales', 89.99, 'Blusas', 'M', 'Blanco', 5, true),
('Vestido Casual Negro', 'Vestido cómodo de algodón ideal para el día a día', 65.50, 'Vestidos', 'S', 'Negro', 8, true),
('Pantalón Clásico', 'Pantalón de corte recto en tela premium', 75.00, 'Pantalones', 'L', 'Azul Marino', 12, true),
('Falda Midi Rosa', 'Falda midi con vuelo en tono rosa pastel', 55.99, 'Faldas', 'M', 'Rosa', 6, true),
('Chaqueta Blazer', 'Blazer estructurado perfecto para looks profesionales', 120.00, 'Chaquetas', 'S', 'Negro', 4, true),
('Blusa Floral', 'Blusa con estampado floral delicado', 68.50, 'Blusas', 'L', 'Multicolor', 7, true),
('Vestido Largo Azul', 'Vestido largo fluido para eventos especiales', 95.00, 'Vestidos', 'M', 'Azul', 3, true),
('Pantalón Wide Leg', 'Pantalón de pierna ancha muy cómodo', 82.00, 'Pantalones', 'S', 'Beige', 9, true),
('Falda Plisada', 'Falda plisada de longitud media', 62.99, 'Faldas', 'L', 'Verde', 5, true),
('Top Crop Blanco', 'Top corto básico de algodón', 35.00, 'Tops', 'S', 'Blanco', 15, true),
('Cardigan Suave', 'Cardigan de punto suave y abrigado', 78.50, 'Chaquetas', 'M', 'Gris', 8, true),
('Vestido Wrap', 'Vestido cruzado muy favorecedor', 72.00, 'Vestidos', 'L', 'Rojo', 6, true),
('Jeans Skinny', 'Jeans ajustados de mezclilla premium', 89.00, 'Pantalones', 'M', 'Azul', 10, true),
('Blusa Satinada', 'Blusa de satén con brillo sutil', 58.99, 'Blusas', 'S', 'Champagne', 4, true),
('Falda Lápiz', 'Falda ajustada de corte lápiz', 67.50, 'Faldas', 'M', 'Negro', 7, true),
('Abrigo Largo', 'Abrigo elegante para temporada fría', 150.00, 'Chaquetas', 'L', 'Camel', 2, true)
ON CONFLICT (id) DO NOTHING;
