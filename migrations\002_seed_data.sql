-- =====================================================
-- Clothing Store Seed Data Migration
-- Version: 002
-- Description: Insert initial categories, colors, and sample products
-- =====================================================

-- =====================================================
-- SEED CATEGORIES
-- =====================================================
INSERT INTO public.categories (name, description, is_active) VALUES
('Blusas', 'Blusas elegantes y casuales para toda ocasión', true),
('Vestidos', 'Vestidos de diferentes estilos y longitudes', true),
('Pantalones', 'Pantalones cómodos y modernos', true),
('Faldas', 'Faldas de diversos cortes y estilos', true),
('Chaquetas', 'Chaquetas y blazers para completar tu look', true),
('Tops', 'Tops y camisetas básicas y de diseño', true)
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- SEED COLORS
-- =====================================================
INSERT INTO public.colors (name, hex_code, is_active) VALUES
('Negro', '#000000', true),
('Blanco', '#FFFFFF', true),
('Gris', '#6B7280', true),
('Azul', '#3B82F6', true),
('Azul Marino', '#1E3A8A', true),
('Rosa', '#EC4899', true),
('Rosa Palo', '#F8BBD9', true),
('Rojo', '#EF4444', true),
('Verde', '#10B981', true),
('Amarillo', '#F59E0B', true),
('Morado', '#8B5CF6', true),
('Naranja', '#F97316', true),
('Beige', '#F5F5DC', true),
('Crema', '#F5F5DC', true),
('Café', '#8B4513', true)
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- SEED SAMPLE PRODUCTS
-- =====================================================
-- Get category IDs for reference
DO $$
DECLARE
    vestidos_id UUID;
    blusas_id UUID;
    pantalones_id UUID;
    faldas_id UUID;
    chaquetas_id UUID;
    tops_id UUID;
BEGIN
    SELECT id INTO vestidos_id FROM public.categories WHERE name = 'Vestidos';
    SELECT id INTO blusas_id FROM public.categories WHERE name = 'Blusas';
    SELECT id INTO pantalones_id FROM public.categories WHERE name = 'Pantalones';
    SELECT id INTO faldas_id FROM public.categories WHERE name = 'Faldas';
    SELECT id INTO chaquetas_id FROM public.categories WHERE name = 'Chaquetas';
    SELECT id INTO tops_id FROM public.categories WHERE name = 'Tops';

    -- Insert sample products
    INSERT INTO public.products (
        name, description, price, category_id, category, 
        sizes, colors, stock_quantity, is_available,
        image_url
    ) VALUES
    (
        'Vestido Midi Elegante',
        'Vestido midi de corte clásico perfecto para ocasiones especiales. Confeccionado en tela de alta calidad con acabados impecables.',
        89.99,
        vestidos_id,
        'Vestidos',
        ARRAY['XS', 'S', 'M', 'L', 'XL'],
        ARRAY['Negro', 'Azul Marino', 'Rojo'],
        15,
        true,
        '/elegant-midi-dress.jpg'
    ),
    (
        'Blusa de Seda Premium',
        'Blusa de seda natural con acabados de lujo. Perfecta para el trabajo o eventos especiales.',
        65.50,
        blusas_id,
        'Blusas',
        ARRAY['XS', 'S', 'M', 'L'],
        ARRAY['Blanco', 'Negro', 'Rosa Palo'],
        20,
        true,
        '/premium-silk-blouse.jpg'
    ),
    (
        'Pantalón Wide Leg',
        'Pantalón de pierna ancha de tela fluida. Cómodo y elegante para cualquier ocasión.',
        75.00,
        pantalones_id,
        'Pantalones',
        ARRAY['S', 'M', 'L', 'XL'],
        ARRAY['Beige', 'Negro', 'Azul Marino'],
        12,
        true,
        '/wide-leg-pants.png'
    ),
    (
        'Falda Plisada Midi',
        'Falda midi con pliegues que aporta movimiento y elegancia a tu look.',
        55.00,
        faldas_id,
        'Faldas',
        ARRAY['XS', 'S', 'M', 'L'],
        ARRAY['Negro', 'Gris', 'Rosa'],
        18,
        true,
        '/pleated-midi-skirt.png'
    ),
    (
        'Blazer Estructurado',
        'Blazer de corte estructurado que define la silueta. Ideal para looks profesionales.',
        120.00,
        chaquetas_id,
        'Chaquetas',
        ARRAY['S', 'M', 'L', 'XL'],
        ARRAY['Negro', 'Gris', 'Azul Marino'],
        8,
        true,
        '/structured-blazer-jacket.jpg'
    ),
    (
        'Top Crop Minimalista',
        'Top crop de diseño minimalista. Perfecto para combinar con faldas o pantalones de tiro alto.',
        35.00,
        tops_id,
        'Tops',
        ARRAY['XS', 'S', 'M', 'L'],
        ARRAY['Blanco', 'Negro', 'Beige'],
        25,
        true,
        '/minimalist-crop-top.jpg'
    ),
    (
        'Vestido Casual Floral',
        'Vestido casual con estampado floral. Ideal para el día a día con un toque femenino.',
        68.00,
        vestidos_id,
        'Vestidos',
        ARRAY['S', 'M', 'L', 'XL'],
        ARRAY['Rosa', 'Azul', 'Verde'],
        14,
        true,
        '/floral-casual-dress.jpg'
    ),
    (
        'Blusa Elegante Manga Larga',
        'Blusa elegante de manga larga con detalles sofisticados. Perfecta para ocasiones especiales.',
        58.00,
        blusas_id,
        'Blusas',
        ARRAY['XS', 'S', 'M', 'L', 'XL'],
        ARRAY['Blanco', 'Crema', 'Rosa Palo'],
        16,
        true,
        '/long-sleeve-elegant-blouse.jpg'
    ),
    (
        'Jeans Skinny Azul',
        'Jeans skinny de corte moderno en denim de alta calidad. Un básico indispensable.',
        85.00,
        pantalones_id,
        'Pantalones',
        ARRAY['XS', 'S', 'M', 'L', 'XL'],
        ARRAY['Azul', 'Azul Marino', 'Negro'],
        22,
        true,
        '/blue-skinny-jeans.jpg'
    ),
    (
        'Falda Mini Negra',
        'Falda mini de corte clásico en color negro. Versátil y elegante para múltiples ocasiones.',
        42.00,
        faldas_id,
        'Faldas',
        ARRAY['XS', 'S', 'M', 'L'],
        ARRAY['Negro'],
        20,
        true,
        '/black-mini-skirt.jpg'
    )
    ON CONFLICT DO NOTHING;
END $$;
