-- Complete Database Setup Script for Minimalist Clothing Store
-- Run this script in your Supabase SQL Editor

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category TEXT NOT NULL,
    size TEXT,
    color TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    is_available BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create admin_users table
CREATE TABLE IF NOT EXISTS public.admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for products table
CREATE POLICY "Anyone can view products" ON public.products
    FOR SELECT USING (true);

CREATE POLICY "Only authenticated users can manage products" ON public.products
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for admin_users table  
CREATE POLICY "Only authenticated users can view admin users" ON public.admin_users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Insert sample products with proper image paths
INSERT INTO public.products (name, description, price, image_url, category, size, color, stock_quantity, is_available) VALUES
('Vestido Midi Elegante', 'Vestido midi de corte clásico perfecto para ocasiones especiales', 89.99, '/elegant-midi-dress.jpg', 'Vestidos', 'M', 'Negro', 15, true),
('Blusa de Seda Premium', 'Blusa de seda natural con acabados de lujo', 65.50, '/premium-silk-blouse.jpg', 'Blusas', 'S', 'Blanco', 20, true),
('Pantalón Wide Leg', 'Pantalón de pierna ancha de tela fluida', 75.00, '/wide-leg-pants.png', 'Pantalones', 'L', 'Beige', 12, true),
('Chaqueta Blazer Estructurada', 'Blazer de corte estructurado para look profesional', 120.00, '/structured-blazer-jacket.jpg', 'Chaquetas', 'M', 'Azul Marino', 8, true),
('Falda Plisada Midi', 'Falda midi con pliegues y cintura alta', 55.99, '/pleated-midi-skirt.png', 'Faldas', 'S', 'Rosa Palo', 18, true),
('Top Crop Minimalista', 'Top crop de diseño minimalista y corte moderno', 35.00, '/minimalist-crop-top.jpg', 'Tops', 'XS', 'Blanco', 25, true),
('Camisa Clásica Blanca', 'Camisa de algodón con corte clásico', 45.00, '/placeholder.svg?height=400&width=300', 'Camisas', 'M', 'Blanco', 30, true),
('Jeans Skinny Azul', 'Jeans ajustados de mezclilla premium', 90.00, '/placeholder.svg?height=400&width=300', 'Jeans', 'L', 'Azul', 22, true),
('Vestido Casual Floral', 'Vestido casual con estampado floral', 65.00, '/placeholder.svg?height=400&width=300', 'Vestidos', 'M', 'Multicolor', 18, true),
('Chaqueta Denim Clásica', 'Chaqueta de mezclilla con corte clásico', 85.00, '/placeholder.svg?height=400&width=300', 'Chaquetas', 'L', 'Azul', 14, true),
('Falda Mini Negra', 'Falda mini de tela elástica', 40.00, '/placeholder.svg?height=400&width=300', 'Faldas', 'S', 'Negro', 25, true),
('Blusa Manga Larga', 'Blusa elegante de manga larga', 55.00, '/placeholder.svg?height=400&width=300', 'Blusas', 'M', 'Crema', 20, true)
ON CONFLICT (id) DO NOTHING;

-- Insert admin user (email: <EMAIL>, password: admin123)
INSERT INTO public.admin_users (email, password_hash) VALUES
('<EMAIL>', '$2a$10$rOvHPGkwQGKqvzjo.6.1/.H.S.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8')
ON CONFLICT (email) DO NOTHING;
