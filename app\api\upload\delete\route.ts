import { NextRequest, NextResponse } from 'next/server'
import { deleteFileFromS3, extractS3KeyFromUrl, isS3Url } from '@/lib/s3/upload'

export async function DELETE(request: NextRequest) {
  try {
    const { url } = await request.json()

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }

    // Validate that this is an S3 URL from our bucket
    if (!isS3Url(url)) {
      return NextResponse.json(
        { error: 'Invalid S3 URL' },
        { status: 400 }
      )
    }

    const key = extractS3KeyFromUrl(url)
    if (!key) {
      return NextResponse.json(
        { error: 'Could not extract S3 key from URL' },
        { status: 400 }
      )
    }

    const result = await deleteFileFromS3(key)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in delete API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
