"use client"

import { useState } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { MessageCircle, Eye } from "lucide-react"
import { generateWhatsAppMessage } from "@/lib/whatsapp"
import { ProductImageModal } from "./product-image-modal"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  additional_images?: string[]
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
}

interface ProductCardProps {
  product: Product
  relatedProducts?: Product[]
}

export function ProductCard({ product, relatedProducts = [] }: ProductCardProps) {
  const [showImageModal, setShowImageModal] = useState(false)
  const isOutOfStock = product.stock_quantity === 0 || !product.is_available
  const isLowStock = product.stock_quantity > 0 && product.stock_quantity <= 3

  const handleWhatsAppClick = () => {
    const message = generateWhatsAppMessage(product)
    const phoneNumber = "573001234567" // Replace with actual WhatsApp business number
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const getImageUrl = () => {
    if (product.image_url) {
      return product.image_url
    }
    const query = `elegant ${product.category.toLowerCase()} ${product.color.toLowerCase()} minimalist fashion clothing`
    return `/placeholder.svg?height=400&width=300&query=${encodeURIComponent(query)}`
  }

  const getColorHex = (colorName: string) => {
    const colorMap: { [key: string]: string } = {
      negro: "#000000",
      blanco: "#FFFFFF",
      gris: "#6B7280",
      azul: "#3B82F6",
      rosa: "#EC4899",
      rojo: "#EF4444",
      verde: "#10B981",
      amarillo: "#F59E0B",
      morado: "#8B5CF6",
      naranja: "#F97316",
    }
    return colorMap[colorName.toLowerCase()] || "#9CA3AF"
  }

  const availableColors = relatedProducts
    .filter((p) => p.name === product.name)
    .map((p) => p.color)
    .filter((color, index, arr) => arr.indexOf(color) === index)

  const availableSizes = relatedProducts
    .filter((p) => p.name === product.name)
    .map((p) => p.size)
    .filter((size, index, arr) => arr.indexOf(size) === index)

  return (
    <>
      <Card className="group overflow-hidden border-border hover:shadow-xl transition-all duration-300 bg-card w-full">
        <div className="relative aspect-[3/4] overflow-hidden bg-muted">
          <Image
            src={getImageUrl() || "/placeholder.svg"}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              const fallbackQuery = `${product.category.toLowerCase()} ${product.color.toLowerCase()} clothing fashion`
              target.src = `/placeholder.svg?height=400&width=300&query=${encodeURIComponent(fallbackQuery)}`
            }}
          />

          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              size="sm"
              variant="secondary"
              className="rounded-full h-10 w-10 p-0 bg-white/90 hover:bg-white shadow-lg"
              onClick={() => setShowImageModal(true)}
            >
              <Eye className="h-5 w-5" />
            </Button>
          </div>

          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {isOutOfStock && (
              <Badge variant="destructive" className="text-xs font-medium">
                Agotado
              </Badge>
            )}
            {isLowStock && !isOutOfStock && (
              <Badge
                variant="secondary"
                className="text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
              >
                ¡Últimas {product.stock_quantity}!
              </Badge>
            )}
          </div>

          <div className="absolute top-2 right-2">
            <Badge variant="outline" className="text-xs bg-background/90 backdrop-blur-sm">
              {product.category}
            </Badge>
          </div>
        </div>

        <CardContent className="p-3 sm:p-4 space-y-2 sm:space-y-3 min-h-[120px] sm:min-h-[140px] flex flex-col">
          <div className="flex items-start justify-between">
            <h3 className="font-medium text-foreground text-xs sm:text-sm leading-tight line-clamp-2 flex-1 mr-2">
              {product.name}
            </h3>
            <div className="text-right">
              <p className="font-bold text-base sm:text-lg text-foreground">DOP${product.price.toLocaleString()}</p>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs px-2 py-1">
              Talla {product.size}
            </Badge>
          </div>

          <div className="flex items-center gap-1 flex-wrap">
            <span className="text-xs text-muted-foreground mr-1">Colores:</span>
            {availableColors.length > 0 ? (
              availableColors.map((color) => (
                <div
                  key={color}
                  className="w-4 h-4 rounded border border-gray-300 flex-shrink-0"
                  style={{ backgroundColor: getColorHex(color) }}
                  title={color}
                />
              ))
            ) : (
              <div
                className="w-4 h-4 rounded border border-gray-300 flex-shrink-0"
                style={{ backgroundColor: getColorHex(product.color) }}
                title={product.color}
              />
            )}
          </div>

          {product.description && (
            <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed flex-1">{product.description}</p>
          )}

          <div className="flex items-center justify-between text-xs mt-auto">
            <span
              className={`font-medium ${isOutOfStock ? "text-destructive" : isLowStock ? "text-orange-600" : "text-green-600"}`}
            >
              {isOutOfStock ? "Sin stock" : `${product.stock_quantity} disponibles`}
            </span>
            <span className="text-muted-foreground flex items-center gap-1">🚚 Envío nacional</span>
          </div>
        </CardContent>

        <CardFooter className="p-3 sm:p-4 pt-0 h-[50px] sm:h-[60px] flex items-center">
          <Button
            onClick={handleWhatsAppClick}
            disabled={isOutOfStock}
            className="w-full bg-green-600 hover:bg-green-700 disabled:bg-muted text-white flex items-center gap-2 font-medium text-xs sm:text-sm"
            size="sm"
          >
            <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="truncate">{isOutOfStock ? "Agotado" : "Comprar por WhatsApp"}</span>
          </Button>
        </CardFooter>
      </Card>

      <ProductImageModal
        product={product}
        relatedProducts={relatedProducts}
        open={showImageModal}
        onOpenChange={setShowImageModal}
      />
    </>
  )
}
