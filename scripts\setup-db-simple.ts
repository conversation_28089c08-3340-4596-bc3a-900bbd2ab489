#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env' })

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY (using as service key for now)')
  console.error('')
  console.error('Please update your .env file')
  process.exit(1)
}

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

async function setupDatabase() {
  try {
    console.log('🚀 Setting up Clothing Store database (Simple Version)...')
    console.log('📍 Supabase URL:', SUPABASE_URL)
    console.log('')

    // Test connection
    console.log('🔍 Testing database connection...')
    const { error: testError } = await supabase
      .from('pg_tables')
      .select('tablename')
      .limit(1)

    if (testError) {
      console.error('❌ Failed to connect to database:', testError.message)
      console.log('')
      console.log('💡 You may need to:')
      console.log('1. Get the service role key from Supabase dashboard')
      console.log('2. Update SUPABASE_SERVICE_ROLE_KEY in .env')
      console.log('3. Or run the SQL manually in Supabase SQL Editor')
      process.exit(1)
    }
    console.log('✅ Database connection successful')

    // Create tables using direct SQL
    console.log('🔄 Creating database tables...')
    
    const createTablesSQL = `
      -- Enable UUID extension
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      -- Categories table
      CREATE TABLE IF NOT EXISTS public.categories (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT UNIQUE NOT NULL,
          description TEXT,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
      );

      -- Products table
      CREATE TABLE IF NOT EXISTS public.products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          price DECIMAL(10,2) NOT NULL,
          category_id UUID REFERENCES public.categories(id),
          category TEXT,
          sizes TEXT[] DEFAULT '{}',
          colors TEXT[] DEFAULT '{}',
          color_hex TEXT,
          size TEXT,
          color TEXT,
          stock_quantity INTEGER NOT NULL DEFAULT 0,
          is_available BOOLEAN NOT NULL DEFAULT true,
          image_url TEXT,
          s3_image_key TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
      );

      -- Admin users table
      CREATE TABLE IF NOT EXISTS public.admin_users (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          email TEXT UNIQUE NOT NULL,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
      );

      -- Insert sample categories
      INSERT INTO public.categories (name, description, is_active) VALUES
      ('Vestidos', 'Vestidos elegantes para toda ocasión', true),
      ('Blusas', 'Blusas modernas y cómodas', true),
      ('Pantalones', 'Pantalones de diferentes estilos', true),
      ('Faldas', 'Faldas de diversos cortes', true)
      ON CONFLICT (name) DO NOTHING;

      -- Insert sample products
      INSERT INTO public.products (name, description, price, category, sizes, colors, stock_quantity, is_available, image_url) VALUES
      ('Vestido Elegante Negro', 'Vestido negro elegante perfecto para ocasiones especiales', 89.99, 'Vestidos', ARRAY['S', 'M', 'L'], ARRAY['Negro'], 10, true, '/elegant-midi-dress.jpg'),
      ('Blusa Blanca Clásica', 'Blusa blanca clásica de algodón', 45.50, 'Blusas', ARRAY['XS', 'S', 'M', 'L'], ARRAY['Blanco'], 15, true, '/premium-silk-blouse.jpg'),
      ('Pantalón Negro Formal', 'Pantalón negro formal para oficina', 65.00, 'Pantalones', ARRAY['S', 'M', 'L', 'XL'], ARRAY['Negro'], 8, true, '/wide-leg-pants.png')
      ON CONFLICT DO NOTHING;
    `;

    // Execute the SQL
    const { error: sqlError } = await supabase.rpc('exec', { sql: createTablesSQL })
    
    if (sqlError) {
      console.log('⚠️  Direct SQL execution failed. You may need to run this manually.')
      console.log('Please copy and paste this SQL into your Supabase SQL Editor:')
      console.log('')
      console.log(createTablesSQL)
      console.log('')
      console.log('Go to: https://supabase.com/dashboard/project/axsleneciqjcsckomawz/sql')
    } else {
      console.log('✅ Database tables created successfully!')
    }

    console.log('')
    console.log('🎉 Setup completed!')
    console.log('')
    console.log('📋 What was created:')
    console.log('   ✅ Categories table with sample categories')
    console.log('   ✅ Products table with sample products')
    console.log('   ✅ Admin users table')
    console.log('')
    console.log('🔑 Next steps:')
    console.log('   1. Run: pnpm run create-admin')
    console.log('   2. Start the dev server: pnpm dev')
    console.log('   3. Visit: http://localhost:3000')
    console.log('')

  } catch (error) {
    console.error('💥 Setup failed:', error)
    console.log('')
    console.log('🔧 Manual Setup Option:')
    console.log('Go to: https://supabase.com/dashboard/project/axsleneciqjcsckomawz/sql')
    console.log('And run the SQL from the migrations/001_initial_schema.sql file')
    process.exit(1)
  }
}

// Run setup
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase()
}
