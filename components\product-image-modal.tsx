"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, MessageCircle } from "lucide-react"
import { generateWhatsAppMessage } from "@/lib/whatsapp"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  additional_images?: string[]
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
}

interface ProductImageModalProps {
  product: Product
  relatedProducts: Product[]
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProductImageModal({ product, relatedProducts, open, onOpenChange }: ProductImageModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [selectedColor, setSelectedColor] = useState(product.color)
  const [selectedSize, setSelectedSize] = useState(product.size)

  // Get all images for the current product
  const allImages = [product.image_url, ...(product.additional_images || [])].filter(Boolean) as string[]

  // Get available variants
  const availableColors = relatedProducts
    .filter((p) => p.name === product.name)
    .map((p) => p.color)
    .filter((color, index, arr) => arr.indexOf(color) === index)

  const availableSizes = relatedProducts
    .filter((p) => p.name === product.name)
    .map((p) => p.size)
    .filter((size, index, arr) => arr.indexOf(size) === index)

  // Get current selected variant
  const selectedVariant =
    relatedProducts.find((p) => p.name === product.name && p.color === selectedColor && p.size === selectedSize) ||
    product

  const getColorHex = (colorName: string) => {
    const colorMap: { [key: string]: string } = {
      negro: "#000000",
      blanco: "#FFFFFF",
      gris: "#6B7280",
      azul: "#3B82F6",
      rosa: "#EC4899",
      rojo: "#EF4444",
      verde: "#10B981",
      amarillo: "#F59E0B",
      morado: "#8B5CF6",
      naranja: "#F97316",
    }
    return colorMap[colorName.toLowerCase()] || "#9CA3AF"
  }

  const handleWhatsAppClick = () => {
    const message = generateWhatsAppMessage(selectedVariant)
    const phoneNumber = "573001234567"
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
  }

  const getImageUrl = (url?: string | null) => {
    if (url) return url
    const query = `elegant ${product.category.toLowerCase()} ${selectedColor.toLowerCase()} minimalist fashion clothing`
    return `/placeholder.svg?height=600&width=500&query=${encodeURIComponent(query)}`
  }

  if (!open) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[95vh] p-0 w-[95vw] sm:w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 h-full max-h-[95vh]">
          {/* Image Gallery */}
          <div className="relative bg-muted">
            <div className="aspect-square lg:aspect-[4/5] relative min-h-[50vh] lg:min-h-[70vh]">
              <Image
                src={getImageUrl(allImages[currentImageIndex]) || "/placeholder.svg"}
                alt={product.name}
                fill
                className="object-cover"
              />

              {/* Navigation arrows */}
              {allImages.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute left-2 top-1/2 -translate-y-1/2 rounded-full h-10 w-10 p-0 bg-white/90 hover:bg-white shadow-lg"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full h-10 w-10 p-0 bg-white/90 hover:bg-white shadow-lg"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </>
              )}
            </div>

            {/* Image indicators */}
            {allImages.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                {allImages.map((_, index) => (
                  <button
                    key={index}
                    className={`w-3 h-3 rounded-full transition-colors shadow-sm ${
                      index === currentImageIndex ? "bg-white" : "bg-white/50"
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="p-4 sm:p-6 space-y-4 sm:space-y-6 overflow-y-auto max-h-[45vh] lg:max-h-none">
            <div>
              <Badge variant="outline" className="mb-2">
                {product.category}
              </Badge>
              <h2 className="text-xl sm:text-2xl font-bold text-balance">{product.name}</h2>
              <p className="text-2xl sm:text-3xl font-bold text-primary mt-2">
                DOP${selectedVariant.price.toLocaleString()}
              </p>
            </div>

            {product.description && (
              <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">{product.description}</p>
            )}

            {/* Color Selection */}
            {availableColors.length > 1 && (
              <div>
                <h3 className="font-medium mb-3 text-sm sm:text-base">Color</h3>
                <div className="flex gap-2 flex-wrap">
                  {availableColors.map((color) => (
                    <button
                      key={color}
                      className={`flex items-center gap-2 px-2 sm:px-3 py-2 rounded-md border transition-colors text-xs sm:text-sm ${
                        selectedColor === color
                          ? "border-primary bg-primary/10"
                          : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => setSelectedColor(color)}
                    >
                      <div className="w-4 h-4 rounded border" style={{ backgroundColor: getColorHex(color) }} />
                      <span>{color}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Size Selection */}
            {availableSizes.length > 1 && (
              <div>
                <h3 className="font-medium mb-3 text-sm sm:text-base">Talla</h3>
                <div className="flex gap-2 flex-wrap">
                  {availableSizes.map((size) => (
                    <button
                      key={size}
                      className={`px-3 sm:px-4 py-2 rounded-md border transition-colors text-xs sm:text-sm ${
                        selectedSize === size ? "border-primary bg-primary/10" : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => setSelectedSize(size)}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Stock Info */}
            <div className="flex items-center justify-between text-xs sm:text-sm">
              <span
                className={`font-medium ${
                  selectedVariant.stock_quantity === 0
                    ? "text-destructive"
                    : selectedVariant.stock_quantity <= 3
                      ? "text-orange-600"
                      : "text-green-600"
                }`}
              >
                {selectedVariant.stock_quantity === 0 ? "Sin stock" : `${selectedVariant.stock_quantity} disponibles`}
              </span>
              <span className="text-muted-foreground">🚚 Envío nacional</span>
            </div>

            <Button
              onClick={handleWhatsAppClick}
              disabled={selectedVariant.stock_quantity === 0}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-muted text-white flex items-center gap-2 font-medium h-12 text-sm sm:text-base"
            >
              <MessageCircle className="h-5 w-5" />
              {selectedVariant.stock_quantity === 0 ? "Producto Agotado" : "Comprar por WhatsApp"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
