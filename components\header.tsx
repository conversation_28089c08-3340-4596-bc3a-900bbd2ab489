import Link from "next/link"

export function Header() {
  return (
    <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="font-light text-2xl border-none text-foreground font-mono">
            {"Yudi fashion"}
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Inicio
            </Link>
            <Link href="#productos" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Productos
            </Link>
            <Link href="#contacto" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Contacto
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
