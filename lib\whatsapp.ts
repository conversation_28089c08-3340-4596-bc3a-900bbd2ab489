// WhatsApp utility functions
export const WHATSAPP_BUSINESS_NUMBER = "573001234567" // Replace with actual business number

export function createWhatsAppUrl(message: string): string {
  return `https://wa.me/${WHATSAPP_BUSINESS_NUMBER}?text=${encodeURIComponent(message)}`
}

export function formatProductMessage(product: {
  name: string
  price: number
  size: string
  color: string
  category: string
  description?: string | null
}): string {
  return `¡Hola! 👋

Me interesa este producto de su tienda:

📦 *${product.name}*
💰 Precio: $${product.price}
📏 Talla: ${product.size}
🎨 Color: ${product.color}
📂 Categoría: ${product.category}

${product.description ? `📝 Descripción: ${product.description}` : ""}

¿Podrían darme más información sobre disponibilidad y envío?

¡Gracias! 😊`
}

export function formatGeneralInquiryMessage(): string {
  return `¡Hola! 👋

Me gustaría obtener más información sobre sus productos de ropa femenina.

¿Podrían ayudarme con:
• Catálogo completo
• Tallas disponibles  
• Precios y promociones
• Información de envíos

¡Gracias! 😊`
}

export function generateWhatsAppMessage(product: {
  name: string
  price: number
  size: string
  color: string
  category: string
  description?: string | null
}): string {
  return formatProductMessage(product)
}
