"use client"

import { MessageCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function WhatsAppFloat() {
  const handleWhatsAppClick = () => {
    const message = `¡Hola! 👋

Me gustaría obtener más información sobre sus productos de ropa femenina.

¿Podrían ayudarme con:
• Catálogo completo
• Tallas disponibles  
• Precios y promociones
• Información de envíos

¡Gracias! 😊`

    const phoneNumber = "573001234567" // Replace with actual WhatsApp business number
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        onClick={handleWhatsAppClick}
        size="lg"
        className="rounded-full bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
      >
        <MessageCircle className="h-6 w-6" />
        <span className="sr-only">Contactar por WhatsApp</span>
      </Button>
    </div>
  )
}
