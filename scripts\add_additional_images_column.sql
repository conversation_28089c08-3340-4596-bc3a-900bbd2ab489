-- Add additional_images column to products table for multiple images support
-- This allows storing multiple image URLs as a JSON array

ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS additional_images TEXT[] DEFAULT '{}';

-- Update existing products to have empty additional_images array if null
UPDATE public.products 
SET additional_images = '{}' 
WHERE additional_images IS NULL;

-- Create index for better performance when querying additional images
CREATE INDEX IF NOT EXISTS idx_products_additional_images ON public.products USING GIN (additional_images);

-- Add comment to document the column
COMMENT ON COLUMN public.products.additional_images IS 'Array of additional image URLs for product gallery';
