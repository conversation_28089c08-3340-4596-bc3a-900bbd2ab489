#!/usr/bin/env tsx

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env' })

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

console.log('🔍 Testing Supabase Connection...')
console.log('📍 Supabase URL:', SUPABASE_URL)
console.log('🔑 Anon Key:', SUPABASE_ANON_KEY ? 'Present' : 'Missing')

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase credentials')
  process.exit(1)
}

// Create Supabase client with anon key first
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testConnection() {
  try {
    console.log('🔄 Testing basic connection...')
    
    // Test basic connection
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1)

    if (error) {
      console.error('❌ Connection failed:', error.message)
      console.log('')
      console.log('💡 This might be normal if tables don\'t exist yet.')
      console.log('   Let\'s try to get the service role key and run setup.')
    } else {
      console.log('✅ Basic connection successful!')
      console.log('📊 Found tables:', data?.length || 0)
    }

    console.log('')
    console.log('📋 Next Steps:')
    console.log('1. Go to https://supabase.com/dashboard/project/axsleneciqjcsckomawz/settings/api')
    console.log('2. Copy the "service_role" key (NOT the anon key)')
    console.log('3. Update SUPABASE_SERVICE_ROLE_KEY in .env')
    console.log('4. Set up your AWS S3 credentials')
    console.log('5. Run: pnpm run setup-db')

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

testConnection()
