# 🚀 Setup Guide - Elegancia Minimalista

## Step 1: Get Supabase Service Role Key

1. Go to your Supabase Dashboard: https://supabase.com/dashboard
2. Select your project: `axsleneciqjcsckomawz`
3. Go to **Settings** → **API**
4. Copy the **service_role** key (NOT the anon key)
5. Update your `.env` file:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
   ```

## Step 2: Set up AWS S3

### Option A: Create New S3 Bucket
1. Go to AWS Console: https://console.aws.amazon.com/s3/
2. Click **Create bucket**
3. Choose a unique name like: `elegancia-minimalista-images`
4. Select region: `us-east-1` (or your preferred region)
5. **Uncheck** "Block all public access" (we need public read access for images)
6. Click **Create bucket**

### Option B: Use Existing Bucket
If you already have an S3 bucket, just use its name.

### Configure Bucket Policy
1. Go to your bucket → **Permissions** → **Bucket policy**
2. Add this policy (replace `YOUR_BUCKET_NAME`):
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::YOUR_BUCKET_NAME/*"
        }
    ]
}
```

## Step 3: Get AWS Credentials

1. Go to AWS Console: https://console.aws.amazon.com/iam/
2. Click **Users** → **Create user**
3. Username: `elegancia-app-user`
4. Attach policy: **AmazonS3FullAccess**
5. Click **Create user**
6. Go to the user → **Security credentials** → **Create access key**
7. Choose **Application running outside AWS**
8. Copy the **Access Key ID** and **Secret Access Key**

## Step 4: Update Environment Variables

Update your `.env` file with the actual values:

```env
# =====================================================
# SUPABASE CONFIGURATION
# =====================================================
NEXT_PUBLIC_SUPABASE_URL=https://axsleneciqjcsckomawz.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4c2xlbmVjaXFqY3Nja29tYXd6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc5NDU0OTAsImV4cCI6MjA3MzUyMTQ5MH0.IjuwZN8X80L4W8x-wcFBlTL0WrxIXVRfnusCyQS-ZdQ
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_from_step_1

# =====================================================
# AWS S3 CONFIGURATION
# =====================================================
AWS_ACCESS_KEY_ID=your_access_key_from_step_3
AWS_SECRET_ACCESS_KEY=your_secret_key_from_step_3
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your_bucket_name_from_step_2
```

## Step 5: Run Database Migrations

Once your environment variables are set up, run:

```bash
# Setup database tables and seed data
pnpm run setup-db

# Create admin user
pnpm run create-admin
```

## Step 6: Start Development Server

```bash
pnpm dev
```

## 🎯 Quick Checklist

- [ ] Got Supabase service role key
- [ ] Created/configured S3 bucket
- [ ] Set bucket policy for public read access
- [ ] Created AWS IAM user with S3 access
- [ ] Updated all environment variables in `.env`
- [ ] Ran `pnpm run setup-db`
- [ ] Ran `pnpm run create-admin`
- [ ] Started dev server with `pnpm dev`

## 🔧 Troubleshooting

### Database Connection Issues
- Make sure your Supabase service role key is correct
- Check that your Supabase project is active

### S3 Upload Issues
- Verify AWS credentials are correct
- Check bucket permissions and policy
- Ensure bucket name is correct and unique

### Migration Issues
- Check database connection first
- Make sure you have the service role key (not anon key)
- Try running migrations manually in Supabase SQL Editor

## 📞 Need Help?

If you encounter any issues:
1. Check the browser console for error messages
2. Check the terminal output for detailed error logs
3. Verify all environment variables are set correctly
4. Make sure your Supabase project and AWS account are active
