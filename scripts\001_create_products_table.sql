-- Create products table for the clothing store
create table if not exists public.products (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  description text,
  price decimal(10,2) not null,
  image_url text,
  category text not null,
  size text not null,
  color text not null,
  stock_quantity integer not null default 0,
  is_available boolean not null default true,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS
alter table public.products enable row level security;

-- Create policies for products (public read access, admin write access)
create policy "Anyone can view products" on public.products for select using (true);

-- Create admin users table for managing the store
create table if not exists public.admin_users (
  id uuid primary key references auth.users(id) on delete cascade,
  email text not null,
  is_admin boolean not null default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS for admin users
alter table public.admin_users enable row level security;

-- Admin policies
create policy "Admins can view admin users" on public.admin_users for select using (
  exists (
    select 1 from public.admin_users 
    where id = auth.uid() and is_admin = true
  )
);

create policy "Admins can insert products" on public.products for insert with check (
  exists (
    select 1 from public.admin_users 
    where id = auth.uid() and is_admin = true
  )
);

create policy "Admins can update products" on public.products for update using (
  exists (
    select 1 from public.admin_users 
    where id = auth.uid() and is_admin = true
  )
);

create policy "Admins can delete products" on public.products for delete using (
  exists (
    select 1 from public.admin_users 
    where id = auth.uid() and is_admin = true
  )
);

-- Function to update updated_at timestamp
create or replace function public.handle_updated_at()
returns trigger
language plpgsql
security definer
set search_path = public
as $$
begin
  new.updated_at = timezone('utc'::text, now());
  return new;
end;
$$;

-- Trigger for products updated_at
create trigger handle_products_updated_at
  before update on public.products
  for each row
  execute function public.handle_updated_at();
