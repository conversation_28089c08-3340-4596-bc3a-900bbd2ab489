import { AdminDashboard } from "@/components/admin/admin-dashboard"
import { createClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"

async function getProducts() {
  try {
    const supabase = await createClient()
    if (!supabase) {
      console.error("Failed to create Supabase client for products")
      return []
    }

    const { data: products, error } = await supabase
      .from("products")
      .select("*")
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching products:", error)
      return []
    }

    return products || []
  } catch (error) {
    console.error("Error in getProducts:", error)
    return []
  }
}

async function getCategories() {
  try {
    const supabase = await createClient()
    if (!supabase) {
      console.error("Failed to create Supabase client for categories")
      return []
    }

    const { data: categories, error } = await supabase.from("categories").select("*").order("name")

    if (error) {
      console.error("Error fetching categories:", error)
      return []
    }

    return categories || []
  } catch (error) {
    console.error("Error in getCategories:", error)
    return []
  }
}

async function getColors() {
  try {
    const supabase = await createClient()
    if (!supabase) {
      console.error("Failed to create Supabase client for colors")
      return []
    }

    const { data: colors, error } = await supabase.from("colors").select("*").order("name")

    if (error) {
      // Check if the error is due to missing table
      if (
        error.message?.includes("Could not find the table") ||
        error.message?.includes("relation") ||
        error.message?.includes("does not exist")
      ) {
        console.log("Colors table not found - needs to be created")
        return null // Return null to indicate table doesn't exist
      }
      console.error("Error fetching colors:", error)
      return []
    }

    return colors || []
  } catch (error) {
    console.error("Error in getColors:", error)
    return []
  }
}

async function checkAuth() {
  try {
    const supabase = await createClient()
    if (!supabase) {
      console.error("Failed to create Supabase client for auth check")
      redirect("/admin/login")
    }

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser()

    if (error || !user) {
      redirect("/admin/login")
    }

    return user
  } catch (error) {
    console.error("Error in checkAuth:", error)
    redirect("/admin/login")
  }
}

export default async function AdminPage() {
  try {
    await checkAuth()
    const products = await getProducts()
    const categories = await getCategories()
    const colors = await getColors()

    return <AdminDashboard products={products} categories={categories} colors={colors} />
  } catch (error) {
    console.error("Error in AdminPage:", error)
    redirect("/admin/login")
  }
}
