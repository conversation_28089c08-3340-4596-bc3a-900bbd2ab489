"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { FileUpload, type UploadedFile } from "@/components/ui/file-upload"
import { X } from "lucide-react"
import { createClient } from "@/lib/supabase/client"
import { updateProductMainImage, getProductImages, deleteAllProductImages } from "@/lib/product-images"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

interface Category {
  id: string
  name: string
}

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
}

interface EditProductDialogProps {
  product: Product
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductUpdated: (product: Product) => void
  colors?: Color[] // Added colors prop for dynamic color selection
}

const AVAILABLE_SIZES = ["XS", "S", "M", "L", "XL", "XXL"]

export function EditProductDialog({
  product,
  open,
  onOpenChange,
  onProductUpdated,
  colors = [],
}: EditProductDialogProps) {
  const [formData, setFormData] = useState({
    name: product.name,
    description: product.description || "",
    price: product.price.toString(),
    category: product.category,
    stock_quantity: product.stock_quantity.toString(),
  })

  const [selectedSizes, setSelectedSizes] = useState<string[]>([product.size])
  const [selectedColors, setSelectedColors] = useState<string[]>([product.color])
  const [uploadedImages, setUploadedImages] = useState<UploadedFile[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [availableColors, setAvailableColors] = useState<Color[]>(colors)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      const supabase = createClient()

      // Fetch categories
      const { data: categoriesData } = await supabase.from("categories").select("*").order("name")
      if (categoriesData) setCategories(categoriesData)

      if (colors.length === 0) {
        const { data: colorsData } = await supabase.from("colors").select("*").eq("is_active", true).order("name")
        if (colorsData) setAvailableColors(colorsData)
      }
    }
    fetchData()
  }, [colors])

  useEffect(() => {
    if (colors.length > 0) {
      setAvailableColors(colors)
    }
  }, [colors])

  const handleSizeToggle = (size: string) => {
    setSelectedSizes((prev) => (prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]))
  }

  const handleColorToggle = (color: string) => {
    setSelectedColors((prev) => (prev.includes(color) ? prev.filter((c) => c !== color) : [...prev, color]))
  }

  const handleFilesUploaded = (files: UploadedFile[]) => {
    setUploadedImages(files)
  }

  const handleFileRemoved = (index: number) => {
    setUploadedImages((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedSizes.length === 0 || selectedColors.length === 0) {
      alert("Debes seleccionar al menos una talla y un color")
      return
    }

    setIsLoading(true)
    const supabase = createClient()

    try {
      // Update main product data
      const { data: updatedProduct, error: updateError } = await supabase
        .from("products")
        .update({
          name: formData.name,
          description: formData.description || null,
          price: Number.parseFloat(formData.price),
          category: formData.category,
          sizes: selectedSizes,
          colors: selectedColors,
          stock_quantity: Number.parseInt(formData.stock_quantity),
          is_available: Number.parseInt(formData.stock_quantity) > 0,
        })
        .eq("id", product.id)
        .select()
        .single()

      if (updateError) throw updateError

      // Handle main image update if new images were uploaded
      if (uploadedImages.length > 0) {
        const mainImage = uploadedImages[0]
        const imageUpdateResult = await updateProductMainImage(
          product.id,
          mainImage.url,
          mainImage.s3Key
        )

        if (!imageUpdateResult.success) {
          console.error("Failed to update main image:", imageUpdateResult.error)
        }
      }

      // Delete all existing product images and replace with new ones
      if (uploadedImages.length > 0) {
        await deleteAllProductImages(product.id)

        // Add new images
        const imageInserts = uploadedImages.map((img, index) => ({
          product_id: product.id,
          image_url: img.url,
          s3_key: img.s3Key,
          is_primary: index === 0,
          sort_order: index,
        }))

        const { error: imagesError } = await supabase
          .from("product_images")
          .insert(imageInserts)

        if (imagesError) {
          console.error("Error adding product images:", imagesError)
        }
      }

      onProductUpdated(updatedProduct)
      onOpenChange(false)
    } catch (error) {
      console.error("Error updating product:", error)
      alert("Error al actualizar el producto")
    }

    setIsLoading(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader>
          <DialogTitle>Editar Producto</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Precio</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Categoría</Label>
            <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Tallas Disponibles</Label>
            <div className="flex flex-wrap gap-3">
              {AVAILABLE_SIZES.map((size) => (
                <div key={size} className="flex items-center space-x-2">
                  <Checkbox
                    id={`size-${size}`}
                    checked={selectedSizes.includes(size)}
                    onCheckedChange={() => handleSizeToggle(size)}
                  />
                  <Label htmlFor={`size-${size}`} className="text-sm font-normal">
                    {size}
                  </Label>
                </div>
              ))}
            </div>
            {selectedSizes.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedSizes.map((size) => (
                  <Badge key={size} variant="secondary">
                    {size}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-3">
            <Label>Colores Disponibles</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto p-2 border rounded-md">
              {availableColors.map((color) => (
                <div key={color.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`color-${color.id}`}
                    checked={selectedColors.includes(color.name)}
                    onCheckedChange={() => handleColorToggle(color.name)}
                  />
                  <div className="w-4 h-4 rounded border flex-shrink-0" style={{ backgroundColor: color.hex_code }} />
                  <Label htmlFor={`color-${color.id}`} className="text-sm font-normal truncate">
                    {color.name}
                  </Label>
                </div>
              ))}
            </div>
            {selectedColors.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedColors.map((color) => (
                  <Badge key={color} variant="secondary" className="text-xs">
                    {color}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="stock">Cantidad en Stock (por variante)</Label>
            <Input
              id="stock"
              type="number"
              value={formData.stock_quantity}
              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
              required
            />
          </div>

          {/* Images Section */}
          <div className="space-y-3">
            <Label>Imágenes del Producto</Label>
            <FileUpload
              onFilesUploaded={handleFilesUploaded}
              onFileRemoved={handleFileRemoved}
              maxFiles={5}
              existingFiles={uploadedImages}
            />
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} className="w-full sm:w-auto">
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
              {isLoading ? "Actualizando..." : "Actualizar Producto"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
