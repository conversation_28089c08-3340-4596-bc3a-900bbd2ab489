"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { createClient } from "@/lib/supabase/client"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

interface Category {
  id: string
  name: string
}

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
}

interface EditProductDialogProps {
  product: Product
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductUpdated: (product: Product) => void
  colors?: Color[] // Added colors prop for dynamic color selection
}

const AVAILABLE_SIZES = ["XS", "S", "M", "L", "XL", "XXL"]

export function EditProductDialog({
  product,
  open,
  onOpenChange,
  onProductUpdated,
  colors = [],
}: EditProductDialogProps) {
  const [formData, setFormData] = useState({
    name: product.name,
    description: product.description || "",
    price: product.price.toString(),
    image_url: product.image_url || "",
    category: product.category,
    stock_quantity: product.stock_quantity.toString(),
  })

  const [selectedSizes, setSelectedSizes] = useState<string[]>([product.size])
  const [selectedColors, setSelectedColors] = useState<string[]>([product.color])
  const [additionalImages, setAdditionalImages] = useState<string[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [availableColors, setAvailableColors] = useState<Color[]>(colors)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchData = async () => {
      const supabase = createClient()

      // Fetch categories
      const { data: categoriesData } = await supabase.from("categories").select("*").order("name")
      if (categoriesData) setCategories(categoriesData)

      if (colors.length === 0) {
        const { data: colorsData } = await supabase.from("colors").select("*").eq("is_active", true).order("name")
        if (colorsData) setAvailableColors(colorsData)
      }
    }
    fetchData()
  }, [colors])

  useEffect(() => {
    if (colors.length > 0) {
      setAvailableColors(colors)
    }
  }, [colors])

  const handleSizeToggle = (size: string) => {
    setSelectedSizes((prev) => (prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]))
  }

  const handleColorToggle = (color: string) => {
    setSelectedColors((prev) => (prev.includes(color) ? prev.filter((c) => c !== color) : [...prev, color]))
  }

  const addImageUrl = () => {
    setAdditionalImages((prev) => [...prev, ""])
  }

  const updateImageUrl = (index: number, url: string) => {
    setAdditionalImages((prev) => prev.map((img, i) => (i === index ? url : img)))
  }

  const removeImageUrl = (index: number) => {
    setAdditionalImages((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedSizes.length === 0 || selectedColors.length === 0) {
      alert("Debes seleccionar al menos una talla y un color")
      return
    }

    setIsLoading(true)
    const supabase = createClient()

    // First delete existing product
    await supabase.from("products").delete().eq("id", product.id)

    // Create new products for each size/color combination
    const newProducts = []
    for (const size of selectedSizes) {
      for (const color of selectedColors) {
        const productData = {
          name: formData.name,
          description: formData.description || null,
          price: Number.parseFloat(formData.price),
          image_url: formData.image_url || null,
          additional_images: additionalImages.filter((img) => img.trim() !== ""),
          category: formData.category,
          size,
          color,
          stock_quantity: Number.parseInt(formData.stock_quantity),
          is_available: Number.parseInt(formData.stock_quantity) > 0,
        }
        newProducts.push(productData)
      }
    }

    const { data, error } = await supabase.from("products").insert(newProducts).select()

    if (error) {
      console.error("Error updating product:", error)
      alert("Error al actualizar el producto")
    } else {
      // Return the first product as representative
      onProductUpdated(data[0])
      onOpenChange(false)
    }

    setIsLoading(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader>
          <DialogTitle>Editar Producto</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Nombre</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>

            <div>
              <Label htmlFor="price">Precio</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>

          <div>
            <Label htmlFor="category">Categoría</Label>
            <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Tallas Disponibles</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {AVAILABLE_SIZES.map((size) => (
                <div key={size} className="flex items-center space-x-2">
                  <Checkbox
                    id={`size-${size}`}
                    checked={selectedSizes.includes(size)}
                    onCheckedChange={() => handleSizeToggle(size)}
                  />
                  <Label htmlFor={`size-${size}`} className="text-sm font-normal">
                    {size}
                  </Label>
                </div>
              ))}
            </div>
            <div className="flex flex-wrap gap-1 mt-2">
              {selectedSizes.map((size) => (
                <Badge key={size} variant="secondary">
                  {size}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label>Colores Disponibles</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mt-2 max-h-48 overflow-y-auto">
              {availableColors.map((color) => (
                <div key={color.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`color-${color.id}`}
                    checked={selectedColors.includes(color.name)}
                    onCheckedChange={() => handleColorToggle(color.name)}
                  />
                  <div className="w-4 h-4 rounded border flex-shrink-0" style={{ backgroundColor: color.hex_code }} />
                  <Label htmlFor={`color-${color.id}`} className="text-sm font-normal truncate">
                    {color.name}
                  </Label>
                </div>
              ))}
            </div>
            <div className="flex flex-wrap gap-1 mt-2">
              {selectedColors.map((color) => (
                <Badge key={color} variant="secondary" className="text-xs">
                  {color}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="stock">Cantidad en Stock (por variante)</Label>
            <Input
              id="stock"
              type="number"
              value={formData.stock_quantity}
              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
              required
            />
          </div>

          <div>
            <Label htmlFor="image_url">Imagen Principal</Label>
            <Input
              id="image_url"
              type="url"
              value={formData.image_url}
              onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
            />
          </div>

          <div>
            <div className="flex items-center justify-between">
              <Label>Imágenes Adicionales</Label>
              <Button type="button" variant="outline" size="sm" onClick={addImageUrl}>
                Agregar Imagen
              </Button>
            </div>
            <div className="space-y-2 mt-2">
              {additionalImages.map((url, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    type="url"
                    value={url}
                    onChange={(e) => updateImageUrl(index, e.target.value)}
                    placeholder="URL de imagen adicional"
                  />
                  <Button type="button" variant="outline" size="sm" onClick={() => removeImageUrl(index)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} className="w-full sm:w-auto">
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
              {isLoading ? "Actualizando..." : "Actualizar Producto"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
