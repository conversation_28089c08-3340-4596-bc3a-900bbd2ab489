"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { createClient } from "@/lib/supabase/client"
import { Trash2, Edit, Plus } from "lucide-react"
import { toast } from "sonner"

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
  created_at: string
}

interface ColorManagerProps {
  colors: Color[]
  onColorAdded: (color: Color) => void
  onColorUpdated: (color: Color) => void
  onColorDeleted: (colorId: string) => void
}

export function ColorManager({ colors, onColorAdded, onColorUpdated, onColorDeleted }: ColorManagerProps) {
  const [isAddDialogO<PERSON>, setIsAddDialogOpen] = useState(false)
  const [editingColor, setEditingColor] = useState<Color | null>(null)
  const [formData, setFormData] = useState({ name: "", hex_code: "#000000" })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const supabase = createClient()

      if (editingColor) {
        // Update existing color
        const { data, error } = await supabase
          .from("colors")
          .update({
            name: formData.name,
            hex_code: formData.hex_code,
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingColor.id)
          .select()
          .single()

        if (error) throw error

        onColorUpdated(data)
        toast.success("Color actualizado exitosamente")
        setEditingColor(null)
      } else {
        // Add new color
        const { data, error } = await supabase
          .from("colors")
          .insert({
            name: formData.name,
            hex_code: formData.hex_code,
          })
          .select()
          .single()

        if (error) throw error

        onColorAdded(data)
        toast.success("Color agregado exitosamente")
        setIsAddDialogOpen(false)
      }

      setFormData({ name: "", hex_code: "#000000" })
    } catch (error) {
      console.error("Error managing color:", error)
      toast.error("Error al gestionar el color")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (colorId: string) => {
    if (!confirm("¿Estás seguro de que quieres eliminar este color?")) return

    try {
      const supabase = createClient()
      const { error } = await supabase.from("colors").delete().eq("id", colorId)

      if (error) throw error

      onColorDeleted(colorId)
      toast.success("Color eliminado exitosamente")
    } catch (error) {
      console.error("Error deleting color:", error)
      toast.error("Error al eliminar el color")
    }
  }

  const handleEdit = (color: Color) => {
    setEditingColor(color)
    setFormData({ name: color.name, hex_code: color.hex_code })
  }

  const resetForm = () => {
    setFormData({ name: "", hex_code: "#000000" })
    setEditingColor(null)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Colores Disponibles</h3>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="w-4 h-4 mr-2" />
              Agregar Color
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Agregar Nuevo Color</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Nombre del Color</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Ej: Azul Cielo"
                  required
                />
              </div>
              <div>
                <Label htmlFor="hex_code">Código de Color</Label>
                <div className="flex gap-2 items-center">
                  <Input
                    id="hex_code"
                    type="color"
                    value={formData.hex_code}
                    onChange={(e) => setFormData({ ...formData, hex_code: e.target.value })}
                    className="w-16 h-10 p-1 border rounded"
                  />
                  <Input
                    value={formData.hex_code}
                    onChange={(e) => setFormData({ ...formData, hex_code: e.target.value })}
                    placeholder="#000000"
                    pattern="^#[0-9A-Fa-f]{6}$"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Guardando..." : "Agregar Color"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {colors.map((color) => (
          <Card key={color.id} className="relative">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full border-2 border-gray-300"
                    style={{ backgroundColor: color.hex_code }}
                  />
                  <CardTitle className="text-sm">{color.name}</CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm" onClick={() => handleEdit(color)} className="h-8 w-8 p-0">
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(color.id)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                <p>Código: {color.hex_code}</p>
                <Badge variant={color.is_active ? "default" : "secondary"} className="mt-1">
                  {color.is_active ? "Activo" : "Inactivo"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingColor} onOpenChange={() => setEditingColor(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Color</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Nombre del Color</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ej: Azul Cielo"
                required
              />
            </div>
            <div>
              <Label htmlFor="edit-hex_code">Código de Color</Label>
              <div className="flex gap-2 items-center">
                <Input
                  id="edit-hex_code"
                  type="color"
                  value={formData.hex_code}
                  onChange={(e) => setFormData({ ...formData, hex_code: e.target.value })}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={formData.hex_code}
                  onChange={(e) => setFormData({ ...formData, hex_code: e.target.value })}
                  placeholder="#000000"
                  pattern="^#[0-9A-Fa-f]{6}$"
                  required
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setEditingColor(null)}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Guardando..." : "Actualizar Color"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
