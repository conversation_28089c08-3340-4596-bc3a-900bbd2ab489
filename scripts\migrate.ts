#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync, readdirSync } from 'fs'
import { join } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// Get current directory
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Environment variables
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

interface Migration {
  version: string
  filename: string
  sql: string
}

async function createMigrationsTable() {
  console.log('📋 Creating migrations table...')
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.migrations (
        id SERIAL PRIMARY KEY,
        version TEXT UNIQUE NOT NULL,
        filename TEXT NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
      );
    `
  })

  if (error) {
    console.error('❌ Failed to create migrations table:', error.message)
    throw error
  }

  console.log('✅ Migrations table ready')
}

async function getExecutedMigrations(): Promise<string[]> {
  const { data, error } = await supabase
    .from('migrations')
    .select('version')
    .order('version')

  if (error) {
    console.error('❌ Failed to get executed migrations:', error.message)
    throw error
  }

  return data?.map(m => m.version) || []
}

async function loadMigrations(): Promise<Migration[]> {
  const migrationsDir = join(__dirname, '..', 'migrations')
  const files = readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort()

  const migrations: Migration[] = []

  for (const filename of files) {
    const version = filename.replace('.sql', '')
    const sql = readFileSync(join(migrationsDir, filename), 'utf-8')
    
    migrations.push({
      version,
      filename,
      sql
    })
  }

  return migrations
}

async function executeMigration(migration: Migration): Promise<void> {
  console.log(`🔄 Executing migration: ${migration.filename}`)

  const { error } = await supabase.rpc('exec_sql', {
    sql: migration.sql
  })

  if (error) {
    console.error(`❌ Failed to execute migration ${migration.filename}:`, error.message)
    throw error
  }

  // Record migration as executed
  const { error: recordError } = await supabase
    .from('migrations')
    .insert({
      version: migration.version,
      filename: migration.filename
    })

  if (recordError) {
    console.error(`❌ Failed to record migration ${migration.filename}:`, recordError.message)
    throw recordError
  }

  console.log(`✅ Migration ${migration.filename} completed`)
}

async function runMigrations() {
  try {
    console.log('🚀 Starting database migrations...')
    console.log('📍 Supabase URL:', SUPABASE_URL)
    
    // Test connection
    const { error: testError } = await supabase.from('migrations').select('count').limit(1)
    if (testError && !testError.message.includes('relation "migrations" does not exist')) {
      console.error('❌ Failed to connect to database:', testError.message)
      process.exit(1)
    }

    await createMigrationsTable()
    
    const executedMigrations = await getExecutedMigrations()
    const allMigrations = await loadMigrations()
    
    console.log(`📊 Found ${allMigrations.length} migration(s)`)
    console.log(`📊 ${executedMigrations.length} migration(s) already executed`)
    
    const pendingMigrations = allMigrations.filter(
      migration => !executedMigrations.includes(migration.version)
    )
    
    if (pendingMigrations.length === 0) {
      console.log('✅ All migrations are up to date!')
      return
    }
    
    console.log(`🔄 Executing ${pendingMigrations.length} pending migration(s)...`)
    
    for (const migration of pendingMigrations) {
      await executeMigration(migration)
    }
    
    console.log('🎉 All migrations completed successfully!')
    
  } catch (error) {
    console.error('💥 Migration failed:', error)
    process.exit(1)
  }
}

// Add exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE OR REPLACE FUNCTION exec_sql(sql text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$;
    `
  }).catch(() => {
    // Function might not exist yet, create it directly
    return supabase.rpc('exec', {
      sql: `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;
      `
    })
  })
}

// Run migrations
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations()
}
