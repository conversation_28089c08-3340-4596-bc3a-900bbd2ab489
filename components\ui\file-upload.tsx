"use client"

import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from './button'
import { Card, CardContent } from './card'
import { Progress } from './progress'
import { X, Upload, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { uploadFileToS3, validateFile, type UploadProgress, type UploadResult } from '@/lib/upload-client'
import { cn } from '@/lib/utils'

export interface UploadedFile {
  file: File
  url: string
  s3Key: string
  progress: number
  status: 'uploading' | 'completed' | 'error'
  error?: string
}

interface FileUploadProps {
  onFilesUploaded: (files: UploadedFile[]) => void
  onFileRemoved: (index: number) => void
  maxFiles?: number
  accept?: Record<string, string[]>
  maxSize?: number
  className?: string
  disabled?: boolean
  existingFiles?: UploadedFile[]
}

export function FileUpload({
  onFilesUploaded,
  onFileRemoved,
  maxFiles = 5,
  accept = {
    'image/*': ['.jpeg', '.jpg', '.png', '.webp']
  },
  maxSize = 5 * 1024 * 1024, // 5MB
  className,
  disabled = false,
  existingFiles = []
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(existingFiles)
  const [isDragActive, setIsDragActive] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (disabled) return

    const filesToUpload = acceptedFiles.slice(0, maxFiles - uploadedFiles.length)
    
    for (const file of filesToUpload) {
      // Validate file
      const validation = validateFile(file)
      if (!validation.valid) {
        console.error('File validation failed:', validation.error)
        continue
      }

      // Create initial file entry
      const fileEntry: UploadedFile = {
        file,
        url: '',
        s3Key: '',
        progress: 0,
        status: 'uploading'
      }

      setUploadedFiles(prev => [...prev, fileEntry])

      // Upload file
      try {
        const result = await uploadFileToS3(file, (progress: UploadProgress) => {
          setUploadedFiles(prev => 
            prev.map((f, i) => 
              i === prev.length - 1 
                ? { ...f, progress: progress.percentage }
                : f
            )
          )
        })

        if (result.success && result.url && result.key) {
          setUploadedFiles(prev => 
            prev.map((f, i) => 
              i === prev.length - 1 
                ? { 
                    ...f, 
                    url: result.url!, 
                    s3Key: result.key!, 
                    status: 'completed',
                    progress: 100
                  }
                : f
            )
          )
        } else {
          setUploadedFiles(prev => 
            prev.map((f, i) => 
              i === prev.length - 1 
                ? { 
                    ...f, 
                    status: 'error',
                    error: result.error || 'Upload failed'
                  }
                : f
            )
          )
        }
      } catch (error) {
        setUploadedFiles(prev => 
          prev.map((f, i) => 
            i === prev.length - 1 
              ? { 
                  ...f, 
                  status: 'error',
                  error: error instanceof Error ? error.message : 'Upload failed'
                }
              : f
          )
        )
      }
    }
  }, [uploadedFiles, maxFiles, disabled])

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    disabled,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
  })

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
    onFileRemoved(index)
  }

  // Notify parent of completed uploads
  React.useEffect(() => {
    const completedFiles = uploadedFiles.filter(f => f.status === 'completed')
    onFilesUploaded(completedFiles)
  }, [uploadedFiles, onFilesUploaded])

  const canUploadMore = uploadedFiles.length < maxFiles

  return (
    <div className={cn('space-y-4', className)}>
      {/* Upload Area */}
      {canUploadMore && (
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
            isDragActive || dropzoneActive
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-primary/50',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
        >
          <input {...getInputProps()} />
          <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            {isDragActive || dropzoneActive
              ? 'Drop the files here...'
              : 'Drag & drop images here, or click to select'}
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Max {maxFiles} files, up to {Math.round(maxSize / 1024 / 1024)}MB each
          </p>
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {uploadedFiles.map((file, index) => (
            <Card key={index} className="relative">
              <CardContent className="p-3">
                <div className="aspect-square relative bg-muted rounded-md overflow-hidden mb-2">
                  {file.status === 'completed' && file.url ? (
                    <img
                      src={file.url}
                      alt={file.file.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                  
                  {/* Remove button */}
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-1 right-1 h-6 w-6 p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* Primary badge */}
                  {index === 0 && (
                    <div className="absolute bottom-1 left-1 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                      Principal
                    </div>
                  )}
                </div>

                {/* File info */}
                <div className="space-y-1">
                  <p className="text-xs font-medium truncate">{file.file.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {(file.file.size / 1024 / 1024).toFixed(2)} MB
                  </p>

                  {/* Progress bar */}
                  {file.status === 'uploading' && (
                    <Progress value={file.progress} className="h-1" />
                  )}

                  {/* Status */}
                  {file.status === 'error' && (
                    <div className="flex items-center gap-1 text-destructive">
                      <AlertCircle className="h-3 w-3" />
                      <span className="text-xs">{file.error || 'Upload failed'}</span>
                    </div>
                  )}

                  {file.status === 'completed' && (
                    <div className="text-xs text-green-600">✓ Uploaded</div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {uploadedFiles.length >= maxFiles && (
        <p className="text-sm text-muted-foreground text-center">
          Maximum number of files reached ({maxFiles})
        </p>
      )}
    </div>
  )
}
