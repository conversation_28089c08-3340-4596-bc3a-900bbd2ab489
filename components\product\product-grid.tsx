"use client"

import { useState } from "react"
import { ProductCard } from "./product-card"
import { Button } from "../ui/button"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  additional_images?: string[]
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
}

interface ProductGridProps {
  products: Product[]
  categories?: string[]
}

export function ProductGrid({ products, categories = [] }: ProductGridProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("Todos")

  const filteredProducts =
    selectedCategory === "Todos" ? products : products.filter((product) => product.category === selectedCategory)

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No hay productos disponibles en este momento.</p>
      </div>
    )
  }

  return (
    <div>
      {categories.length > 0 && (
        <div className="mb-8">
          <div className="flex flex-wrap justify-center gap-2">
            <Button
              variant={selectedCategory === "Todos" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("Todos")}
              className="rounded-full"
            >
              Todos ({products.length})
            </Button>
            {categories.map((category) => {
              const count = products.filter((p) => p.category === category).length
              return (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="rounded-full"
                >
                  {category} ({count})
                </Button>
              )
            })}
          </div>
        </div>
      )}

      <section id="productos" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            relatedProducts={products.filter((p) => p.name === product.name)}
          />
        ))}
      </section>

      {filteredProducts.length === 0 && selectedCategory !== "Todos" && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No hay productos disponibles en la categoría "{selectedCategory}".</p>
        </div>
      )}
    </div>
  )
}
