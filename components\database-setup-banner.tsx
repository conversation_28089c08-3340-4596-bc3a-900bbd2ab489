"use client"

import { AlertCircle, Database, ExternalLink, Copy, Check } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useState } from "react"

const SQL_SCRIPT = `-- Complete Database Setup Script for Minimalist Clothing Store
-- Run this script in your Supabase SQL Editor

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category TEXT NOT NULL,
    size TEXT,
    color TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    is_available BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create admin_users table
CREATE TABLE IF NOT EXISTS public.admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies for products table
CREATE POLICY "Anyone can view products" ON public.products
    FOR SELECT USING (true);

CREATE POLICY "Only authenticated users can manage products" ON public.products
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for admin_users table  
CREATE POLICY "Only authenticated users can view admin users" ON public.admin_users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Insert sample products with proper image paths
INSERT INTO public.products (name, description, price, image_url, category, size, color, stock_quantity, is_available) VALUES
('Vestido Midi Elegante', 'Vestido midi de corte clásico perfecto para ocasiones especiales', 89.99, '/elegant-midi-dress.jpg', 'Vestidos', 'M', 'Negro', 15, true),
('Blusa de Seda Premium', 'Blusa de seda natural con acabados de lujo', 65.50, '/premium-silk-blouse.jpg', 'Blusas', 'S', 'Blanco', 20, true),
('Pantalón Wide Leg', 'Pantalón de pierna ancha de tela fluida', 75.00, '/wide-leg-pants.png', 'Pantalones', 'L', 'Beige', 12, true),
('Chaqueta Blazer Estructurada', 'Blazer de corte estructurado para look profesional', 120.00, '/structured-blazer-jacket.jpg', 'Chaquetas', 'M', 'Azul Marino', 8, true),
('Falda Plisada Midi', 'Falda midi con pliegues y cintura alta', 55.99, '/pleated-midi-skirt.png', 'Faldas', 'S', 'Rosa Palo', 18, true),
('Top Crop Minimalista', 'Top crop de diseño minimalista y corte moderno', 35.00, '/minimalist-crop-top.jpg', 'Tops', 'XS', 'Blanco', 25, true),
('Camisa Clásica Blanca', 'Camisa de algodón con corte clásico', 45.00, '/white-classic-shirt.jpg', 'Camisas', 'M', 'Blanco', 30, true),
('Jeans Skinny Azul', 'Jeans ajustados de mezclilla premium', 90.00, '/blue-skinny-jeans.jpg', 'Jeans', 'L', 'Azul', 22, true),
('Vestido Casual Floral', 'Vestido casual con estampado floral', 65.00, '/floral-casual-dress.jpg', 'Vestidos', 'M', 'Multicolor', 18, true),
('Chaqueta Denim Clásica', 'Chaqueta de mezclilla con corte clásico', 85.00, '/classic-denim-jacket.png', 'Chaquetas', 'L', 'Azul', 14, true),
('Falda Mini Negra', 'Falda mini de tela elástica', 40.00, '/black-mini-skirt.jpg', 'Faldas', 'S', 'Negro', 25, true),
('Blusa Manga Larga', 'Blusa elegante de manga larga', 55.00, '/long-sleeve-elegant-blouse.jpg', 'Blusas', 'M', 'Crema', 20, true)
ON CONFLICT (id) DO NOTHING;

-- Insert admin user (email: <EMAIL>, password: admin123)
INSERT INTO public.admin_users (email, password_hash) VALUES
('<EMAIL>', '$2a$10$rOvHPGkwQGKqvzjo.6.1/.H.S.*******.*******.*******.*******.8')
ON CONFLICT (email) DO NOTHING;`

export function DatabaseSetupBanner() {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(SQL_SCRIPT)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error("Failed to copy text: ", err)
    }
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-950/20 border-b border-yellow-200 dark:border-yellow-800">
      <div className="container mx-auto px-4 py-6">
        <Alert className="border-yellow-200 dark:border-yellow-800">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Configuración de Base de Datos Requerida</AlertTitle>
          <AlertDescription className="mt-2">
            Las tablas de la base de datos no están configuradas. Sigue estos pasos para completar la configuración.
          </AlertDescription>
        </Alert>

        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Configuración de Base de Datos
            </CardTitle>
            <CardDescription>
              Ejecuta este script SQL en tu panel de Supabase para crear todas las tablas y productos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="font-medium mb-3 text-blue-800 dark:text-blue-200">Pasos Rápidos:</h4>
              <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-2 list-decimal list-inside">
                <li>Copia el script SQL completo usando el botón de abajo</li>
                <li>
                  Ve a tu proyecto en{" "}
                  <a
                    href="https://supabase.com/dashboard"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline font-medium"
                  >
                    supabase.com/dashboard
                  </a>
                </li>
                <li>
                  Abre el <strong>"SQL Editor"</strong> desde el menú lateral
                </li>
                <li>
                  Pega el script y haz clic en <strong>"Run"</strong>
                </li>
                <li>Recarga esta página para ver los productos</li>
              </ol>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Script SQL Completo</h4>
                <Button
                  onClick={copyToClipboard}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 bg-transparent"
                >
                  {copied ? (
                    <>
                      <Check className="h-4 w-4" />
                      ¡Copiado!
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      Copiar Script
                    </>
                  )}
                </Button>
              </div>

              <div className="relative">
                <pre className="p-4 bg-muted rounded-lg text-xs overflow-x-auto max-h-64 border">
                  <code>{SQL_SCRIPT}</code>
                </pre>
              </div>
            </div>

            <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-medium mb-2 text-green-800 dark:text-green-200">¿Qué incluye este script?</h4>
              <ul className="text-sm text-green-700 dark:text-green-300 space-y-1 list-disc list-inside">
                <li>
                  <strong>Tabla de productos:</strong> 12 productos de ejemplo con imágenes
                </li>
                <li>
                  <strong>Tabla de administradores:</strong> Usuario admin (<EMAIL> / admin123)
                </li>
                <li>
                  <strong>Políticas de seguridad:</strong> RLS habilitado para proteger los datos
                </li>
                <li>
                  <strong>Configuración completa:</strong> Todo listo para usar inmediatamente
                </li>
              </ul>
            </div>

            <div className="flex gap-2">
              <Button asChild>
                <a href="https://supabase.com/dashboard" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Abrir Supabase Dashboard
                </a>
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()}>
                Verificar Configuración
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
