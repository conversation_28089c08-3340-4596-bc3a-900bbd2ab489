"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { EditProductDialog } from "../forms/edit-product-dialog"
import { createClient } from "@/lib/supabase/client"
import { Trash2, Edit } from "lucide-react"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
}

interface ProductTableProps {
  products: Product[]
  onProductUpdated: (product: Product) => void
  onProductDeleted: (productId: string) => void
  colors?: Color[] // Added colors prop to pass to EditProductDialog
}

export function ProductTable({ products, onProductUpdated, onProductDeleted, colors = [] }: ProductTableProps) {
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  const handleDelete = async (productId: string) => {
    if (!confirm("¿Estás seguro de que quieres eliminar este producto?")) return

    setIsDeleting(productId)
    const supabase = createClient()

    const { error } = await supabase.from("products").delete().eq("id", productId)

    if (error) {
      console.error("Error deleting product:", error)
      alert("Error al eliminar el producto")
    } else {
      onProductDeleted(productId)
    }

    setIsDeleting(null)
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nombre</TableHead>
            <TableHead>Categoría</TableHead>
            <TableHead>Precio</TableHead>
            <TableHead>Talla</TableHead>
            <TableHead>Color</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Estado</TableHead>
            <TableHead>Acciones</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id}>
              <TableCell className="font-medium">{product.name}</TableCell>
              <TableCell>{product.category}</TableCell>
              <TableCell>${product.price}</TableCell>
              <TableCell>{product.size}</TableCell>
              <TableCell>{product.color}</TableCell>
              <TableCell>{product.stock_quantity}</TableCell>
              <TableCell>
                <Badge variant={product.is_available && product.stock_quantity > 0 ? "default" : "secondary"}>
                  {product.is_available && product.stock_quantity > 0 ? "Disponible" : "Agotado"}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => setEditingProduct(product)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(product.id)}
                    disabled={isDeleting === product.id}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {editingProduct && (
        <EditProductDialog
          product={editingProduct}
          open={!!editingProduct}
          onOpenChange={(open) => !open && setEditingProduct(null)}
          onProductUpdated={onProductUpdated}
          colors={colors} // Pass colors to EditProductDialog
        />
      )}
    </>
  )
}
