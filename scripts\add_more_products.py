import os
import psycopg2
from psycopg2.extras import RealDictCursor

def setup_database_with_products():
    try:
        # Conectar a la base de datos
        conn = psycopg2.connect(os.environ['POSTGRES_URL'])
        cur = conn.cursor(cursor_factory=RealDictCursor)
        
        print("[v0] Conectado a la base de datos")
        
        # Crear tabla si no existe
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS public.products (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            image_url TEXT,
            category TEXT NOT NULL,
            size TEXT NOT NULL,
            color TEXT NOT NULL,
            stock_quantity INTEGER NOT NULL DEFAULT 0,
            is_available BOOLEAN NOT NULL DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );
        """
        
        cur.execute(create_table_sql)
        print("[v0] Tabla products creada o ya existe")
        
        # Configurar RLS
        rls_sql = """
        ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
        DROP POLICY IF EXISTS "Anyone can view products" ON public.products;
        CREATE POLICY "Anyone can view products" ON public.products FOR SELECT USING (true);
        """
        
        cur.execute(rls_sql)
        print("[v0] Políticas RLS configuradas")
        
        # Limpiar productos existentes
        cur.execute("DELETE FROM public.products;")
        print("[v0] Productos existentes eliminados")
        
        # Insertar muchos productos variados
        products = [
            # Blusas
            ('Blusa Elegante Seda', 'Blusa de seda con diseño minimalista perfecto para ocasiones especiales', 89.99, 'Blusas', 'S', 'Blanco', 12, True),
            ('Blusa Casual Algodón', 'Blusa cómoda de algodón orgánico para uso diario', 45.50, 'Blusas', 'M', 'Negro', 8, True),
            ('Blusa Estampada', 'Blusa con estampado floral delicado', 52.00, 'Blusas', 'L', 'Rosa', 6, True),
            ('Blusa Manga Larga', 'Blusa elegante de manga larga', 67.99, 'Blusas', 'S', 'Azul', 0, False),
            
            # Vestidos
            ('Vestido Midi Elegante', 'Vestido midi perfecto para eventos formales', 125.00, 'Vestidos', 'M', 'Negro', 4, True),
            ('Vestido Casual Verano', 'Vestido ligero ideal para días calurosos', 78.50, 'Vestidos', 'S', 'Blanco', 10, True),
            ('Vestido Largo Noche', 'Vestido largo para ocasiones especiales', 189.99, 'Vestidos', 'L', 'Azul marino', 3, True),
            ('Vestido Corto Playero', 'Vestido corto perfecto para la playa', 55.00, 'Vestidos', 'M', 'Coral', 7, True),
            
            # Pantalones
            ('Pantalón Palazzo', 'Pantalón palazzo cómodo y elegante', 72.00, 'Pantalones', 'M', 'Negro', 9, True),
            ('Jeans Skinny', 'Jeans ajustados de alta calidad', 95.50, 'Pantalones', 'S', 'Azul', 15, True),
            ('Pantalón Lino', 'Pantalón de lino para verano', 68.99, 'Pantalones', 'L', 'Beige', 5, True),
            ('Leggings Deportivos', 'Leggings cómodos para ejercicio', 35.00, 'Pantalones', 'M', 'Negro', 20, True),
            
            # Faldas
            ('Falda Midi Plisada', 'Falda midi con pliegues elegantes', 58.50, 'Faldas', 'S', 'Gris', 8, True),
            ('Falda Corta Denim', 'Falda corta de mezclilla', 42.00, 'Faldas', 'M', 'Azul', 12, True),
            ('Falda Larga Bohemia', 'Falda larga con estilo bohemio', 75.99, 'Faldas', 'L', 'Multicolor', 4, True),
            
            # Chaquetas
            ('Blazer Clásico', 'Blazer clásico para look profesional', 145.00, 'Chaquetas', 'M', 'Negro', 6, True),
            ('Chaqueta Denim', 'Chaqueta de mezclilla versátil', 85.50, 'Chaquetas', 'S', 'Azul', 0, False),
            ('Cardigan Suave', 'Cardigan suave de punto', 62.99, 'Chaquetas', 'L', 'Gris', 11, True),
            
            # Accesorios
            ('Bolso Elegante', 'Bolso de cuero sintético elegante', 95.00, 'Accesorios', 'Único', 'Marrón', 8, True),
            ('Collar Minimalista', 'Collar dorado con diseño minimalista', 28.50, 'Accesorios', 'Único', 'Dorado', 25, True),
            ('Pañuelo Seda', 'Pañuelo de seda con estampado exclusivo', 35.99, 'Accesorios', 'Único', 'Multicolor', 15, True),
        ]
        
        insert_sql = """
        INSERT INTO public.products (name, description, price, category, size, color, stock_quantity, is_available)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cur.executemany(insert_sql, products)
        print(f"[v0] {len(products)} productos insertados exitosamente")
        
        # Verificar inserción
        cur.execute("SELECT COUNT(*) as total FROM public.products;")
        result = cur.fetchone()
        print(f"[v0] Total de productos en la base de datos: {result['total']}")
        
        # Mostrar algunos productos por categoría
        cur.execute("SELECT category, COUNT(*) as count FROM public.products GROUP BY category ORDER BY category;")
        categories = cur.fetchall()
        print("[v0] Productos por categoría:")
        for cat in categories:
            print(f"  - {cat['category']}: {cat['count']} productos")
        
        conn.commit()
        print("[v0] ✅ Base de datos configurada exitosamente con productos variados")
        
    except Exception as e:
        print(f"[v0] ❌ Error: {e}")
        if conn:
            conn.rollback()
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    setup_database_with_products()
