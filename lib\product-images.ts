import { createClient } from '@/lib/supabase/client'
import { deleteFileFromS3, extractS3KeyFromUrl, isS3Url } from '@/lib/s3/upload'

export interface ProductImage {
  id: string
  product_id: string
  image_url: string
  s3_key: string
  alt_text: string | null
  is_primary: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface ImageUpdateResult {
  success: boolean
  error?: string
  deletedImages?: string[]
}

/**
 * Get all images for a product
 */
export async function getProductImages(productId: string): Promise<ProductImage[]> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('product_images')
    .select('*')
    .eq('product_id', productId)
    .order('sort_order')

  if (error) {
    console.error('Error fetching product images:', error)
    return []
  }

  return data || []
}

/**
 * Delete images from both S3 and database
 */
export async function deleteProductImages(imageIds: string[]): Promise<ImageUpdateResult> {
  if (imageIds.length === 0) {
    return { success: true, deletedImages: [] }
  }

  const supabase = createClient()
  const deletedImages: string[] = []

  try {
    // First, get the image data to extract S3 keys
    const { data: images, error: fetchError } = await supabase
      .from('product_images')
      .select('id, image_url, s3_key')
      .in('id', imageIds)

    if (fetchError) {
      return { success: false, error: fetchError.message }
    }

    if (!images || images.length === 0) {
      return { success: true, deletedImages: [] }
    }

    // Delete from S3 first
    for (const image of images) {
      let s3Key = image.s3_key

      // If s3_key is not available, try to extract from URL
      if (!s3Key && image.image_url && isS3Url(image.image_url)) {
        s3Key = extractS3KeyFromUrl(image.image_url)
      }

      if (s3Key) {
        const deleteResult = await deleteFileFromS3(s3Key)
        if (deleteResult.success) {
          deletedImages.push(image.image_url)
        } else {
          console.warn(`Failed to delete S3 file ${s3Key}:`, deleteResult.error)
        }
      }
    }

    // Delete from database
    const { error: deleteError } = await supabase
      .from('product_images')
      .delete()
      .in('id', imageIds)

    if (deleteError) {
      return { success: false, error: deleteError.message }
    }

    return { success: true, deletedImages }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Update product images - handles adding new images and removing old ones
 */
export async function updateProductImages(
  productId: string,
  newImages: Array<{
    image_url: string
    s3_key: string
    alt_text?: string
    is_primary?: boolean
    sort_order: number
  }>,
  imagesToDelete: string[] = []
): Promise<ImageUpdateResult> {
  const supabase = createClient()

  try {
    // Delete old images first
    if (imagesToDelete.length > 0) {
      const deleteResult = await deleteProductImages(imagesToDelete)
      if (!deleteResult.success) {
        return deleteResult
      }
    }

    // Add new images
    if (newImages.length > 0) {
      const { error: insertError } = await supabase
        .from('product_images')
        .insert(
          newImages.map(img => ({
            product_id: productId,
            image_url: img.image_url,
            s3_key: img.s3_key,
            alt_text: img.alt_text || null,
            is_primary: img.is_primary || false,
            sort_order: img.sort_order
          }))
        )

      if (insertError) {
        return { success: false, error: insertError.message }
      }
    }

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Replace all images for a product
 */
export async function replaceProductImages(
  productId: string,
  newImages: Array<{
    image_url: string
    s3_key: string
    alt_text?: string
    is_primary?: boolean
    sort_order: number
  }>
): Promise<ImageUpdateResult> {
  const supabase = createClient()

  try {
    // Get all existing images
    const existingImages = await getProductImages(productId)
    const existingImageIds = existingImages.map(img => img.id)

    // Delete all existing images
    if (existingImageIds.length > 0) {
      const deleteResult = await deleteProductImages(existingImageIds)
      if (!deleteResult.success) {
        return deleteResult
      }
    }

    // Add new images
    if (newImages.length > 0) {
      const { error: insertError } = await supabase
        .from('product_images')
        .insert(
          newImages.map(img => ({
            product_id: productId,
            image_url: img.image_url,
            s3_key: img.s3_key,
            alt_text: img.alt_text || null,
            is_primary: img.is_primary || false,
            sort_order: img.sort_order
          }))
        )

      if (insertError) {
        return { success: false, error: insertError.message }
      }
    }

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

/**
 * Delete all images for a product (used when deleting a product)
 */
export async function deleteAllProductImages(productId: string): Promise<ImageUpdateResult> {
  const existingImages = await getProductImages(productId)
  const existingImageIds = existingImages.map(img => img.id)

  if (existingImageIds.length === 0) {
    return { success: true, deletedImages: [] }
  }

  return deleteProductImages(existingImageIds)
}

/**
 * Update main product image and S3 key
 */
export async function updateProductMainImage(
  productId: string,
  imageUrl: string,
  s3Key: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient()

  try {
    // Get current main image to delete from S3
    const { data: currentProduct, error: fetchError } = await supabase
      .from('products')
      .select('image_url, s3_image_key')
      .eq('id', productId)
      .single()

    if (fetchError) {
      return { success: false, error: fetchError.message }
    }

    // Delete old main image from S3 if it exists
    if (currentProduct?.s3_image_key) {
      await deleteFileFromS3(currentProduct.s3_image_key)
    } else if (currentProduct?.image_url && isS3Url(currentProduct.image_url)) {
      const oldKey = extractS3KeyFromUrl(currentProduct.image_url)
      if (oldKey) {
        await deleteFileFromS3(oldKey)
      }
    }

    // Update product with new main image
    const { error: updateError } = await supabase
      .from('products')
      .update({
        image_url: imageUrl,
        s3_image_key: s3Key
      })
      .eq('id', productId)

    if (updateError) {
      return { success: false, error: updateError.message }
    }

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}
