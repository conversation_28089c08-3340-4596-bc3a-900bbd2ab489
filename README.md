# Elegancia Minimalista - Tienda de Ropa Femenina

Una tienda online minimalista y moderna para venta de ropa femenina con panel de administración integrado.

## Características

### Tienda Principal
- ✅ Diseño minimalista y moderno
- ✅ Catálogo de productos con imágenes
- ✅ Información de stock en tiempo real
- ✅ Indicador de productos agotados
- ✅ Precios y detalles de productos
- ✅ Información de envío nacional
- ✅ Integración completa con WhatsApp

### Panel de Administración
- ✅ Autenticación segura con Supabase
- ✅ Dashboard con estadísticas
- ✅ Gestión completa de productos (CRUD)
- ✅ Control de inventario
- ✅ Interfaz intuitiva y responsive

### Funcionalidades WhatsApp
- ✅ Botón flotante de contacto
- ✅ Mensajes personalizados por producto
- ✅ Información detallada en cada mensaje
- ✅ Fácil integración con WhatsApp Business

## Configuración

### 1. Base de Datos
Ejecuta los scripts SQL en orden:
1. `scripts/001_create_products_table.sql` - Crea las tablas principales
2. `scripts/002_seed_sample_products.sql` - Datos de ejemplo
3. `scripts/003_create_admin_user.sql` - Usuario administrador

### 2. WhatsApp Business
Actualiza el número de WhatsApp en:
- `components/product-card.tsx`
- `components/whatsapp-float.tsx`
- `lib/whatsapp.ts`

Reemplaza `573001234567` con tu número de WhatsApp Business.

### 3. Usuario Administrador
1. Regístrate en `/admin/login` con tu email
2. Actualiza el email en `scripts/003_create_admin_user.sql`
3. Ejecuta el script para obtener permisos de administrador

## Estructura del Proyecto

\`\`\`
├── app/
│   ├── admin/           # Panel de administración
│   ├── page.tsx         # Página principal de la tienda
│   └── layout.tsx       # Layout principal
├── components/
│   ├── admin/           # Componentes del panel admin
│   ├── product-card.tsx # Tarjeta de producto
│   ├── whatsapp-float.tsx # Botón flotante WhatsApp
│   └── ...
├── lib/
│   ├── supabase/        # Configuración Supabase
│   └── whatsapp.ts      # Utilidades WhatsApp
└── scripts/             # Scripts SQL
\`\`\`

## Uso

### Para Clientes
1. Navega por el catálogo de productos
2. Haz clic en "Comprar por WhatsApp" en cualquier producto
3. Se abrirá WhatsApp con un mensaje pre-formateado
4. Completa tu compra a través de WhatsApp

### Para Administradores
1. Accede a `/admin/login`
2. Inicia sesión con credenciales de administrador
3. Gestiona productos desde el dashboard
4. Controla inventario y precios en tiempo real

## Tecnologías

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui
- **Base de Datos**: Supabase (PostgreSQL)
- **Autenticación**: Supabase Auth
- **Comunicación**: WhatsApp Business API
