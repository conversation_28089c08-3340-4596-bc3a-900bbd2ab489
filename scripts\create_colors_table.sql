-- Create colors table for dynamic color management
CREATE TABLE IF NOT EXISTS public.colors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    hex_code TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS for colors table
ALTER TABLE public.colors ENABLE ROW LEVEL SECURITY;

-- Create policies for colors
CREATE POLICY "Anyone can view colors" ON public.colors
    FOR SELECT USING (true);

CREATE POLICY "Only authenticated users can manage colors" ON public.colors
    FOR ALL USING (auth.role() = 'authenticated');

-- Insert default colors
INSERT INTO public.colors (name, hex_code) VALUES
('Negro', '#000000'),
('Blanco', '#FFFFFF'),
('Azul', '#3B82F6'),
('<PERSON>', '#EC4899'),
('Be<PERSON>', '#F5F5DC'),
('Crema', '#F5F5DC'),
('Azul Marino', '#1E3A8A'),
('Rosa Palo', '#F8BBD9'),
('Gris', '#6B7280'),
('Verde', '#10B981'),
('Amarillo', '#F59E0B'),
('Morado', '#8B5CF6'),
('Rojo', '#EF4444'),
('Naranja', '#F97316'),
('Café', '#92400E')
ON CONFLICT (name) DO NOTHING;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_colors_active ON public.colors(is_active);
