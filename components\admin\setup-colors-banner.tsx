"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Database, Play, CheckCircle, AlertCircle } from "lucide-react"

export function SetupColorsBanner() {
  const [isExecuting, setIsExecuting] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const executeScript = async () => {
    setIsExecuting(true)
    setResult(null)

    try {
      const response = await fetch("/api/admin/setup-colors", {
        method: "POST",
      })

      const data = await response.json()

      if (response.ok) {
        setResult({ success: true, message: data.message })
        // Refresh the page after successful setup
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      } else {
        setResult({ success: false, message: data.error || "Error desconocido" })
      }
    } catch (error) {
      setResult({ success: false, message: "Error de conexión" })
    } finally {
      setIsExecuting(false)
    }
  }

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <Database className="h-5 w-5" />
          Configuración Requerida: Sistema de Colores
        </CardTitle>
        <CardDescription className="text-orange-700">
          La tabla de colores no existe en tu base de datos. Ejecuta la configuración para habilitar la gestión dinámica
          de colores.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-orange-700">
          <p className="font-medium mb-2">Esta configuración creará:</p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Tabla de colores con políticas de seguridad</li>
            <li>15 colores predefinidos (Negro, Blanco, Azul, etc.)</li>
            <li>Sistema de gestión desde el panel admin</li>
          </ul>
        </div>

        {result && (
          <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={result.success ? "text-green-800" : "text-red-800"}>
                {result.message}
              </AlertDescription>
            </div>
          </Alert>
        )}

        <Button onClick={executeScript} disabled={isExecuting} className="bg-orange-600 hover:bg-orange-700 text-white">
          <Play className="h-4 w-4 mr-2" />
          {isExecuting ? "Configurando..." : "Configurar Sistema de Colores"}
        </Button>
      </CardContent>
    </Card>
  )
}
