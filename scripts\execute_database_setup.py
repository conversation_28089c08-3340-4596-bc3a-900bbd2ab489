import os
import psycopg2
from urllib.parse import urlparse

def execute_sql_file(cursor, file_path):
    """Execute a SQL file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        print(f"Ejecutando {file_path}...")
        cursor.execute(sql_content)
        print(f"✅ {file_path} ejecutado exitosamente")
        return True
    except FileNotFoundError:
        print(f"❌ Archivo no encontrado: {file_path}")
        return False
    except Exception as e:
        print(f"❌ Error ejecutando {file_path}: {str(e)}")
        return False

def main():
    # Get database URL from environment
    database_url = os.getenv('POSTGRES_URL')
    if not database_url:
        print("❌ No se encontró POSTGRES_URL en las variables de entorno")
        return
    
    try:
        # Parse the database URL
        parsed = urlparse(database_url)
        
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            database=parsed.path[1:],  # Remove leading slash
            user=parsed.username,
            password=parsed.password
        )
        
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("🔗 Conectado a la base de datos")
        
        # Execute SQL files in order
        sql_files = [
            'scripts/001_create_products_table.sql',
            'scripts/002_seed_sample_products.sql'
        ]
        
        success_count = 0
        for sql_file in sql_files:
            if execute_sql_file(cursor, sql_file):
                success_count += 1
        
        # Verify tables were created
        cursor.execute("SELECT COUNT(*) FROM public.products;")
        product_count = cursor.fetchone()[0]
        print(f"✅ Base de datos configurada exitosamente")
        print(f"📦 {product_count} productos en el catálogo")
        print(f"🎯 {success_count}/{len(sql_files)} scripts ejecutados correctamente")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {str(e)}")
        print("🔧 Verifica que las variables de entorno de Supabase estén configuradas correctamente")

if __name__ == "__main__":
    main()
