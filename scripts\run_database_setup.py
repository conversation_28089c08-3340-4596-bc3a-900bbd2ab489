import os
import subprocess
import sys

def run_sql_script(script_path):
    """Execute a SQL script using the Supabase connection"""
    print(f"Ejecutando script: {script_path}")
    
    # Read the SQL file
    with open(script_path, 'r', encoding='utf-8') as file:
        sql_content = file.read()
    
    print(f"Contenido del script {script_path}:")
    print("=" * 50)
    print(sql_content)
    print("=" * 50)
    print()

def main():
    """Run all database setup scripts in order"""
    scripts = [
        "scripts/001_create_products_table.sql",
        "scripts/002_seed_sample_products.sql", 
        "scripts/003_create_admin_user.sql"
    ]
    
    print("🚀 Configurando base de datos para la tienda de ropa...")
    print()
    
    for script in scripts:
        if os.path.exists(script):
            run_sql_script(script)
        else:
            print(f"❌ Script no encontrado: {script}")
    
    print("✅ Scripts de base de datos ejecutados.")
    print()
    print("📋 Próximos pasos:")
    print("1. Ve a tu proyecto Supabase")
    print("2. Abre el SQL Editor")
    print("3. Ejecuta cada script SQL mostrado arriba en orden")
    print("4. O copia y pega el contenido de cada script en el SQL Editor")
    print()
    print("🔐 Para crear un usuario administrador:")
    print("1. Primero regístrate en /admin/login con tu email")
    print("2. Luego ejecuta el script 003_create_admin_user.sql")
    print("3. Reemplaza '<EMAIL>' con tu email real")

if __name__ == "__main__":
    main()
