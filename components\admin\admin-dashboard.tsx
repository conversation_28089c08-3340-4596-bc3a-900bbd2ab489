"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ProductTable } from "./product-table"
import { createClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import { CategoryManager } from "./category-manager"
import { ColorManager } from "./color-manager"
import { EnhancedAddProductDialog } from "../forms/enhanced-add-product-dialog"
import { SetupColorsBanner } from "./setup-colors-banner"

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

interface Category {
  id: string
  name: string
  description?: string
  created_at: string
}

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
  created_at: string
}

interface AdminDashboardProps {
  products: Product[]
  categories: Category[]
  colors: Color[] | null // Allow null to indicate table doesn't exist
}

export function AdminDashboard({
  products: initialProducts,
  categories: initialCategories,
  colors: initialColors,
}: AdminDashboardProps) {
  const [products, setProducts] = useState(initialProducts || [])
  const [categories, setCategories] = useState(initialCategories || [])
  const [colors, setColors] = useState(initialColors || [])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("products")
  const router = useRouter()

  const colorsTableExists = initialColors !== null

  const handleLogout = async () => {
    const supabase = createClient()
    await supabase.auth.signOut()
    router.push("/admin/login")
  }

  const handleProductAdded = (newProduct: Product) => {
    setProducts([newProduct, ...products])
    setIsAddDialogOpen(false)
  }

  const handleProductUpdated = (updatedProduct: Product) => {
    setProducts(products.map((p) => (p.id === updatedProduct.id ? updatedProduct : p)))
  }

  const handleProductDeleted = (productId: string) => {
    setProducts(products.filter((p) => p.id !== productId))
  }

  const handleCategoryAdded = (newCategory: Category) => {
    setCategories([...categories, newCategory])
  }

  const handleCategoryUpdated = (updatedCategory: Category) => {
    setCategories(categories.map((c) => (c.id === updatedCategory.id ? updatedCategory : c)))
  }

  const handleCategoryDeleted = (categoryId: string) => {
    setCategories(categories.filter((c) => c.id !== categoryId))
  }

  const handleColorAdded = (newColor: Color) => {
    setColors([...colors, newColor])
  }

  const handleColorUpdated = (updatedColor: Color) => {
    setColors(colors.map((c) => (c.id === updatedColor.id ? updatedColor : c)))
  }

  const handleColorDeleted = (colorId: string) => {
    setColors(colors.filter((c) => c.id !== colorId))
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-light text-foreground">Panel de Administración</h1>
            <Button variant="outline" onClick={handleLogout}>
              Cerrar Sesión
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {!colorsTableExists && (
          <div className="mb-6">
            <SetupColorsBanner />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Productos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{products?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Disponibles</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {products?.filter((p) => p.is_available && p.stock_quantity > 0).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Agotados</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {products?.filter((p) => !p.is_available || p.stock_quantity === 0).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Stock Total</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{products?.reduce((sum, p) => sum + p.stock_quantity, 0) || 0}</div>
            </CardContent>
          </Card>
        </div>

        <div className="mb-6">
          <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
            <button
              onClick={() => setActiveTab("products")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "products"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              Productos
            </button>
            <button
              onClick={() => setActiveTab("categories")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "categories"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              Categorías
            </button>
            {colorsTableExists && (
              <button
                onClick={() => setActiveTab("colors")}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "colors"
                    ? "bg-background text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                Colores
              </button>
            )}
          </div>
        </div>

        {activeTab === "products" && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Gestión de Productos</CardTitle>
                <Button onClick={() => setIsAddDialogOpen(true)}>Agregar Producto</Button>
              </div>
            </CardHeader>
            <CardContent>
              <ProductTable
                products={products}
                onProductUpdated={handleProductUpdated}
                onProductDeleted={handleProductDeleted}
                colors={colorsTableExists ? colors : []} // Pass empty array if table doesn't exist
              />
            </CardContent>
          </Card>
        )}

        {activeTab === "categories" && (
          <Card>
            <CardHeader>
              <CardTitle>Gestión de Categorías</CardTitle>
            </CardHeader>
            <CardContent>
              <CategoryManager
                categories={categories}
                onCategoryAdded={handleCategoryAdded}
                onCategoryUpdated={handleCategoryUpdated}
                onCategoryDeleted={handleCategoryDeleted}
              />
            </CardContent>
          </Card>
        )}

        {activeTab === "colors" && colorsTableExists && (
          <Card>
            <CardHeader>
              <CardTitle>Gestión de Colores</CardTitle>
            </CardHeader>
            <CardContent>
              <ColorManager
                colors={colors}
                onColorAdded={handleColorAdded}
                onColorUpdated={handleColorUpdated}
                onColorDeleted={handleColorDeleted}
              />
            </CardContent>
          </Card>
        )}

        <EnhancedAddProductDialog
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          onProductAdded={handleProductAdded}
          categories={categories}
          colors={colorsTableExists ? colors : []} // Pass empty array if table doesn't exist
        />
      </main>
    </div>
  )
}
