"use server"

import { createClient } from "@/lib/supabase/server"

export async function setupDatabase() {
  const supabase = await createClient()

  try {
    // Check if products table exists by trying to query it
    const { error: checkError } = await supabase.from("products").select("id").limit(1)

    // If no error, table exists
    if (!checkError) {
      return { success: true, message: "Database already set up" }
    }

    // Create products table
    const { error: createTableError } = await supabase.rpc("exec_sql", {
      sql: `
        -- Create products table
        CREATE TABLE IF NOT EXISTS public.products (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          price DECIMAL(10,2) NOT NULL,
          image_url TEXT,
          category TEXT NOT NULL,
          size TEXT,
          color TEXT,
          is_available BOOLEAN DEFAULT true,
          stock_quantity INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Create admin_users table
        CREATE TABLE IF NOT EXISTS public.admin_users (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          password_hash TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Enable RLS
        ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

        -- Create policies
        CREATE POLICY "Products are viewable by everyone" ON public.products
          FOR SELECT USING (true);

        CREATE POLICY "Only authenticated users can manage products" ON public.products
          FOR ALL USING (auth.role() = 'authenticated');

        CREATE POLICY "Only authenticated users can view admin users" ON public.admin_users
          FOR SELECT USING (auth.role() = 'authenticated');

        -- Insert sample products
        INSERT INTO public.products (name, description, price, image_url, category, size, color, stock_quantity) VALUES
        ('Vestido Midi Elegante', 'Vestido midi de corte clásico perfecto para ocasiones especiales', 89.99, '/elegant-midi-dress.jpg', 'Vestidos', 'M', 'Negro', 15),
        ('Blusa de Seda Premium', 'Blusa de seda natural con acabados de lujo', 65.50, '/premium-silk-blouse.jpg', 'Blusas', 'S', 'Blanco', 20),
        ('Pantalón Wide Leg', 'Pantalón de pierna ancha de tela fluida', 75.00, '/wide-leg-pants.png', 'Pantalones', 'L', 'Beige', 12),
        ('Chaqueta Blazer Estructurada', 'Blazer de corte estructurado para look profesional', 120.00, '/structured-blazer-jacket.jpg', 'Chaquetas', 'M', 'Azul Marino', 8),
        ('Falda Plisada Midi', 'Falda midi con pliegues y cintura alta', 55.99, '/pleated-midi-skirt.png', 'Faldas', 'S', 'Rosa Palo', 18),
        ('Top Crop Minimalista', 'Top crop de diseño minimalista y corte moderno', 35.00, '/minimalist-crop-top.jpg', 'Tops', 'XS', 'Blanco', 25);

        -- Insert admin user (password: admin123)
        INSERT INTO public.admin_users (email, password_hash) VALUES
        ('<EMAIL>', '$2a$10$rOvHPGkwQGKqvzjo.6.1/.H.S.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8.8');
      `,
    })

    if (createTableError) {
      console.error("Error creating tables:", createTableError)
      return { success: false, error: createTableError.message }
    }

    return { success: true, message: "Database set up successfully" }
  } catch (error) {
    console.error("Setup database error:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}
