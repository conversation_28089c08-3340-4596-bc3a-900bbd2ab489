"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { FileUpload, type UploadedFile } from "@/components/ui/file-upload"
import { X, Plus } from "lucide-react"
import { createClient } from "@/lib/supabase/client"

interface Category {
  id: string
  name: string
  is_active: boolean
}

interface Color {
  id: string
  name: string
  hex_code: string
  is_active: boolean
}

interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  sizes: string[]
  colors: string[]
  color_hex: string | null
  stock_quantity: number
  is_available: boolean
  created_at: string
  updated_at: string
}

interface EnhancedAddProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductAdded: (product: Product) => void
  categories: Category[]
  colors: Color[] // Added colors prop for dynamic color selection
}

const AVAILABLE_SIZES = ["XS", "S", "M", "L", "XL", "XXL"]

export function EnhancedAddProductDialog({
  open,
  onOpenChange,
  onProductAdded,
  categories,
  colors,
}: EnhancedAddProductDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    category_id: "",
    stock_quantity: "",
  })
  const [selectedSizes, setSelectedSizes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<Color[]>([])
  const [customColor, setCustomColor] = useState({ name: "", hex: "#000000" })
  const [uploadedImages, setUploadedImages] = useState<UploadedFile[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const supabase = createClient()

  const handleSizeToggle = (size: string) => {
    setSelectedSizes((prev) => (prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]))
  }

  const handleColorSelect = (color: Color) => {
    if (!selectedColors.find((c) => c.id === color.id)) {
      setSelectedColors((prev) => [...prev, color])
    }
  }

  const handleColorRemove = (colorId: string) => {
    setSelectedColors((prev) => prev.filter((c) => c.id !== colorId))
  }

  const handleAddCustomColor = async () => {
    if (customColor.name && customColor.hex) {
      try {
        const { data: newColor, error } = await supabase
          .from("colors")
          .insert({
            name: customColor.name,
            hex_code: customColor.hex,
          })
          .select()
          .single()

        if (error) throw error

        // Add to selected colors
        if (!selectedColors.find((c) => c.name === newColor.name)) {
          setSelectedColors((prev) => [...prev, newColor])
        }

        setCustomColor({ name: "", hex: "#000000" })
      } catch (error) {
        console.error("Error adding custom color:", error)
        alert("Error al agregar el color personalizado")
      }
    }
  }

  const handleFilesUploaded = (files: UploadedFile[]) => {
    setUploadedImages(files)
  }

  const handleFileRemoved = (index: number) => {
    setUploadedImages((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Create product
      const { data: product, error: productError } = await supabase
        .from("products")
        .insert({
          name: formData.name,
          description: formData.description || null,
          price: Number.parseFloat(formData.price),
          category_id: formData.category_id,
          sizes: selectedSizes,
          colors: selectedColors.map((c) => c.name),
          color_hex: selectedColors[0]?.hex_code || null,
          stock_quantity: Number.parseInt(formData.stock_quantity),
          is_available: Number.parseInt(formData.stock_quantity) > 0,
          image_url: uploadedImages[0]?.url || null, // Keep for backward compatibility
          s3_image_key: uploadedImages[0]?.s3Key || null,
        })
        .select()
        .single()

      if (productError) throw productError

      // Add product images
      if (uploadedImages.length > 0) {
        const imageInserts = uploadedImages.map((img, index) => ({
          product_id: product.id,
          image_url: img.url,
          s3_key: img.s3Key,
          is_primary: index === 0,
          sort_order: index,
        }))

        const { error: imagesError } = await supabase.from("product_images").insert(imageInserts)

        if (imagesError) throw imagesError
      }

      onProductAdded(product)

      // Reset form
      setFormData({
        name: "",
        description: "",
        price: "",
        category_id: "",
        stock_quantity: "",
      })
      setSelectedSizes([])
      setSelectedColors([])
      setUploadedImages([])
      onOpenChange(false)
    } catch (error) {
      console.error("Error adding product:", error)
      alert("Error al agregar el producto")
    }

    setIsLoading(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Agregar Nuevo Producto</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="price">Precio</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Categoría</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) => setFormData({ ...formData, category_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="stock">Cantidad en Stock</Label>
              <Input
                id="stock"
                type="number"
                value={formData.stock_quantity}
                onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
                required
              />
            </div>
          </div>

          {/* Sizes */}
          <div className="space-y-3">
            <Label>Tallas Disponibles</Label>
            <div className="flex flex-wrap gap-2">
              {AVAILABLE_SIZES.map((size) => (
                <Button
                  key={size}
                  type="button"
                  variant={selectedSizes.includes(size) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSizeToggle(size)}
                >
                  {size}
                </Button>
              ))}
            </div>
            {selectedSizes.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {selectedSizes.map((size) => (
                  <Badge key={size} variant="secondary">
                    {size}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Colors */}
          <div className="space-y-3">
            <Label>Colores Disponibles</Label>
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
              {colors
                .filter((color) => color.is_active)
                .map((color) => (
                  <Button
                    key={color.id}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-16 p-2 bg-transparent hover:bg-muted/50 transition-colors"
                    onClick={() => handleColorSelect(color)}
                  >
                    <div className="flex flex-col items-center gap-1">
                      <div
                        className="w-6 h-6 rounded-full border-2 border-border shadow-sm"
                        style={{ backgroundColor: color.hex_code }}
                      />
                      <span className="text-xs font-medium truncate w-full text-center">{color.name}</span>
                    </div>
                  </Button>
                ))}
            </div>

            {/* Custom Color */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Agregar Color Personalizado</Label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="Nombre del color"
                    value={customColor.name}
                    onChange={(e) => setCustomColor({ ...customColor, name: e.target.value })}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={customColor.hex}
                    onChange={(e) => setCustomColor({ ...customColor, hex: e.target.value })}
                    className="w-12 h-10 p-1 border rounded cursor-pointer"
                    title="Seleccionar color"
                  />
                  <Button
                    type="button"
                    onClick={handleAddCustomColor}
                    size="sm"
                    disabled={!customColor.name.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {selectedColors.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Colores Seleccionados</Label>
                <div className="flex flex-wrap gap-2">
                  {selectedColors.map((color) => (
                    <Badge key={color.id} variant="secondary" className="flex items-center gap-2 px-3 py-1">
                      <div className="w-3 h-3 rounded-full border" style={{ backgroundColor: color.hex_code }} />
                      <span>{color.name}</span>
                      <span
                        className="h-3 w-3 cursor-pointer hover:text-destructive transition-colors"
                        onClick={() => handleColorRemove(color.id)}
                      >
                        <X className="h-3 w-3" />
                      </span>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Images */}
          <div className="space-y-3">
            <Label>Imágenes del Producto</Label>
            <FileUpload
              onFilesUploaded={handleFilesUploaded}
              onFileRemoved={handleFileRemoved}
              maxFiles={5}
              existingFiles={uploadedImages}
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Agregando..." : "Agregar Producto"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
