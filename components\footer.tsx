import { MessageCircle, Truck, Shield, CreditCard } from "lucide-react"

export function Footer() {
  return (
    <footer className="border-t border-border bg-muted/30 mt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <MessageCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h4 className="font-medium text-sm">Compra por WhatsApp</h4>
              <p className="text-xs text-muted-foreground">Atención personalizada</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Truck className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-sm">Envío Nacional</h4>
              <p className="text-xs text-muted-foreground">Menos de 24 horas</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Shield className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium text-sm">Calidad Garantizada</h4>
              <p className="text-xs text-muted-foreground">Productos premium</p>
            </div>
          </div>
        </div>

        <div className="mb-8 p-6 bg-card rounded-lg border">
          <div className="text-center mb-4">
            <div className="flex items-center justify-center gap-2 mb-2">
              <CreditCard className="h-5 w-5 text-blue-600" />
              <h3 className="font-medium text-foreground">Métodos de Pago</h3>
            </div>
            <p className="text-sm text-muted-foreground">Paga de forma segura y conveniente</p>
          </div>
          <div className="flex items-center justify-center gap-8">
            <div className="flex flex-col items-center gap-2 p-4 border-2 border-border rounded-lg bg-background hover:bg-muted/50 transition-colors w-32">
              <div className="h-16 w-24 flex items-center justify-center">
                <img src="/banreservas-logo.png" alt="Banreservas" className="max-h-12 max-w-20 object-contain" />
              </div>
              <span className="text-sm font-medium text-foreground">Banreservas</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 border-2 border-border rounded-lg bg-background hover:bg-muted/50 transition-colors w-32">
              <div className="h-16 w-24 flex items-center justify-center">
                <img src="/qik-logo.png" alt="Qik" className="max-h-12 max-w-20 object-contain" />
              </div>
              <span className="text-sm font-medium text-foreground">Qik</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="font-medium text-foreground mb-4">Tienda de Ropa Femenina</h3>
            <p className="text-sm text-muted-foreground">Ropa femenina de calidad y elegante.</p>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-4">Envíos</h4>
            <p className="text-sm text-muted-foreground">
              Realizamos envíos a todo el país. Tiempo de entrega: menos de 24 Horas.
            </p>
          </div>

          <div id="contacto">
            <h4 className="font-medium text-foreground mb-4">Contacto</h4>
            <p className="text-sm text-muted-foreground">
              ¿Tienes preguntas? Contáctanos por WhatsApp para una atención personalizada.
            </p>
          </div>
        </div>

        <div className="border-t border-border mt-8 pt-8 text-center">
          <p className="text-sm text-muted-foreground">
            © 2025 Yudi fashion. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </footer>
  )
}
