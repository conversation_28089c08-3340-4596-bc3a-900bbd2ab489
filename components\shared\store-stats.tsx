interface Product {
  id: string
  name: string
  description: string | null
  price: number
  image_url: string | null
  category: string
  size: string
  color: string
  stock_quantity: number
  is_available: boolean
}

interface StoreStatsProps {
  products: Product[]
}

export function StoreStats({ products }: StoreStatsProps) {
  const totalProducts = products.length
  const availableProducts = products.filter((p) => p.is_available && p.stock_quantity > 0).length
  const categories = [...new Set(products.map((p) => p.category))].length
  const averagePrice =
    products.length > 0 ? (products.reduce((sum, p) => sum + p.price, 0) / products.length).toFixed(0) : 0

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <div className="text-center p-4 bg-card rounded-lg border">
        <div className="text-2xl font-bold text-foreground">{totalProducts}</div>
        <div className="text-sm text-muted-foreground">Productos</div>
      </div>
      <div className="text-center p-4 bg-card rounded-lg border">
        <div className="text-2xl font-bold text-green-600">{availableProducts}</div>
        <div className="text-sm text-muted-foreground">Disponibles</div>
      </div>
      <div className="text-center p-4 bg-card rounded-lg border">
        <div className="text-2xl font-bold text-foreground">{categories}</div>
        <div className="text-sm text-muted-foreground">Categorías</div>
      </div>
      <div className="text-center p-4 bg-card rounded-lg border">
        <div className="text-2xl font-bold text-foreground">${averagePrice}</div>
        <div className="text-sm text-muted-foreground">Precio promedio</div>
      </div>
    </div>
  )
}
