-- Insert sample clothing products
insert into public.products (name, description, price, image_url, category, size, color, stock_quantity, is_available) values
('Blusa Elegante', 'Blusa de seda con diseño minimalista perfecto para ocasiones especiales', 89.99, '/placeholder.svg?height=400&width=300', 'Blusas', 'M', 'Blanco', 5, true),
('Vestido Casual', 'Vestido cómodo de algodón ideal para el día a día', 65.50, '/placeholder.svg?height=400&width=300', 'Vestidos', 'S', 'Negro', 8, true),
('Pantalón Clásico', 'Pantalón de corte recto en tela premium', 75.00, '/placeholder.svg?height=400&width=300', 'Pantalones', 'L', 'Azul Marino', 3, true),
('Falda Midi', 'Falda midi con pliegues, perfecta para looks profesionales', 55.99, '/placeholder.svg?height=400&width=300', '<PERSON>aldas', 'M', '<PERSON><PERSON>', 0, false),
('<PERSON>queta Blazer', 'Blazer estructurado para un look sofisticado', 120.00, '/placeholder.svg?height=400&width=300', 'Chaquetas', 'S', 'Negro', 4, true),
('Top Básico', 'Top básico de algodón orgánico', 35.00, '/placeholder.svg?height=400&width=300', 'Tops', 'M', 'Blanco', 12, true),
('Jeans Skinny', 'Jeans de corte skinny en denim premium', 85.00, '/placeholder.svg?height=400&width=300', 'Pantalones', 'S', 'Azul', 6, true),
('Cardigan Suave', 'Cardigan de punto suave ideal para entretiempo', 68.99, '/placeholder.svg?height=400&width=300', 'Chaquetas', 'L', 'Beige', 7, true);
