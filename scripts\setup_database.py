import os
import psycopg2
from urllib.parse import urlparse

def execute_sql_script(cursor, script_content, script_name):
    """Execute a SQL script"""
    try:
        print(f"Ejecutando {script_name}...")
        cursor.execute(script_content)
        print(f"✅ {script_name} ejecutado exitosamente")
        return True
    except Exception as e:
        print(f"❌ Error ejecutando {script_name}: {str(e)}")
        return False

def main():
    # Get database URL from environment
    database_url = os.getenv('POSTGRES_URL')
    if not database_url:
        print("❌ No se encontró POSTGRES_URL en las variables de entorno")
        return
    
    try:
        # Parse the database URL
        parsed = urlparse(database_url)
        
        # Connect to database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            database=parsed.path[1:],  # Remove leading slash
            user=parsed.username,
            password=parsed.password
        )
        
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("🔗 Conectado a la base de datos")
        
        # Script 1: Create tables
        script1 = """
        -- Create products table for the clothing store
        CREATE TABLE IF NOT EXISTS public.products (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          name text NOT NULL,
          description text,
          price decimal(10,2) NOT NULL,
          image_url text,
          category text NOT NULL,
          size text NOT NULL,
          color text NOT NULL,
          stock_quantity integer NOT NULL DEFAULT 0,
          is_available boolean NOT NULL DEFAULT true,
          created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Enable RLS
        ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

        -- Create policies for products (public read access)
        DROP POLICY IF EXISTS "Anyone can view products" ON public.products;
        CREATE POLICY "Anyone can view products" ON public.products FOR SELECT USING (true);

        -- Create admin users table
        CREATE TABLE IF NOT EXISTS public.admin_users (
          id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
          email text NOT NULL,
          is_admin boolean NOT NULL DEFAULT false,
          created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Enable RLS for admin users
        ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;

        -- Function to update updated_at timestamp
        CREATE OR REPLACE FUNCTION public.handle_updated_at()
        RETURNS trigger
        LANGUAGE plpgsql
        SECURITY DEFINER
        SET search_path = public
        AS $$
        BEGIN
          NEW.updated_at = timezone('utc'::text, now());
          RETURN NEW;
        END;
        $$;

        -- Trigger for products updated_at
        DROP TRIGGER IF EXISTS handle_products_updated_at ON public.products;
        CREATE TRIGGER handle_products_updated_at
          BEFORE UPDATE ON public.products
          FOR EACH ROW
          EXECUTE FUNCTION public.handle_updated_at();
        """
        
        execute_sql_script(cursor, script1, "Crear tablas")
        
        # Script 2: Insert sample products
        script2 = """
        -- Insert sample clothing products (only if table is empty)
        INSERT INTO public.products (name, description, price, image_url, category, size, color, stock_quantity, is_available)
        SELECT * FROM (VALUES
          ('Blusa Elegante', 'Blusa de seda con diseño minimalista perfecto para ocasiones especiales', 89.99, '/placeholder.svg?height=400&width=300', 'Blusas', 'M', 'Blanco', 5, true),
          ('Vestido Casual', 'Vestido cómodo de algodón ideal para el día a día', 65.50, '/placeholder.svg?height=400&width=300', 'Vestidos', 'S', 'Negro', 8, true),
          ('Pantalón Clásico', 'Pantalón de corte recto en tela premium', 75.00, '/placeholder.svg?height=400&width=300', 'Pantalones', 'L', 'Azul Marino', 3, true),
          ('Falda Midi', 'Falda midi con pliegues, perfecta para looks profesionales', 55.99, '/placeholder.svg?height=400&width=300', 'Faldas', 'M', 'Gris', 0, false),
          ('Chaqueta Blazer', 'Blazer estructurado para un look sofisticado', 120.00, '/placeholder.svg?height=400&width=300', 'Chaquetas', 'S', 'Negro', 4, true),
          ('Top Básico', 'Top básico de algodón orgánico', 35.00, '/placeholder.svg?height=400&width=300', 'Tops', 'M', 'Blanco', 12, true),
          ('Jeans Skinny', 'Jeans de corte skinny en denim premium', 85.00, '/placeholder.svg?height=400&width=300', 'Pantalones', 'S', 'Azul', 6, true),
          ('Cardigan Suave', 'Cardigan de punto suave ideal para entretiempo', 68.99, '/placeholder.svg?height=400&width=300', 'Chaquetas', 'L', 'Beige', 7, true)
        ) AS v(name, description, price, image_url, category, size, color, stock_quantity, is_available)
        WHERE NOT EXISTS (SELECT 1 FROM public.products LIMIT 1);
        """
        
        execute_sql_script(cursor, script2, "Insertar productos de ejemplo")
        
        # Verify tables were created
        cursor.execute("SELECT COUNT(*) FROM public.products;")
        product_count = cursor.fetchone()[0]
        print(f"✅ Base de datos configurada exitosamente")
        print(f"📦 {product_count} productos en el catálogo")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {str(e)}")
        print("🔧 Verifica que las variables de entorno de Supabase estén configuradas correctamente")

if __name__ == "__main__":
    main()
